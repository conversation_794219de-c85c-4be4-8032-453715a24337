# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.29

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: sorting
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = E$:\software\sorting\build-Debug\
# =============================================================================
# Object build statements for EXECUTABLE target sorting


#############################################
# Order-only phony target for sorting

build cmake_object_order_depends_target_sorting: phony || sorting_autogen sorting_autogen\EWIEGA46WW\qrc_qml.cpp

build CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj: CXX_COMPILER__sorting_unscanned_Debug E$:\software\sorting\build-Debug\sorting_autogen\mocs_compilation.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IE:\software\sorting\build-Debug -IE:\software\sorting -IE:\software\sorting\build-Debug\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtANGLE -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtWidgets
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir\sorting_autogen
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb

build CMakeFiles\sorting.dir\main.cpp.obj: CXX_COMPILER__sorting_unscanned_Debug E$:\software\sorting\main.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IE:\software\sorting\build-Debug -IE:\software\sorting -IE:\software\sorting\build-Debug\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtANGLE -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtWidgets
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb

build CMakeFiles\sorting.dir\sorting.cpp.obj: CXX_COMPILER__sorting_unscanned_Debug E$:\software\sorting\sorting.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IE:\software\sorting\build-Debug -IE:\software\sorting -IE:\software\sorting\build-Debug\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtANGLE -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtWidgets
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb

build CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj: CXX_COMPILER__sorting_unscanned_Debug E$:\software\sorting\build-Debug\sorting_autogen\EWIEGA46WW\qrc_qml.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++17
  INCLUDES = -IE:\software\sorting\build-Debug -IE:\software\sorting -IE:\software\sorting\build-Debug\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtANGLE -ID:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtWidgets
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb


# =============================================================================
# Link build statements for EXECUTABLE target sorting


#############################################
# Link the executable sorting.exe

build sorting.exe: CXX_EXECUTABLE_LINKER__sorting_Debug CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj CMakeFiles\sorting.dir\main.cpp.obj CMakeFiles\sorting.dir\sorting.cpp.obj CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj | D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Quickd.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Widgetsd.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Qmld.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Networkd.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Guid.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Cored.lib || sorting_autogen
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Quickd.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Widgetsd.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Qmld.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Networkd.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Guid.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Cored.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\sorting.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_FILE = sorting.exe
  TARGET_IMPLIB = sorting.lib
  TARGET_PDB = sorting.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build-Debug && "C:\Program Files\CMake\bin\cmake-gui.exe" -SE:\software\sorting -BE:\software\sorting\build-Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build-Debug && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SE:\software\sorting -BE:\software\sorting\build-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Utility command for sorting_autogen

build sorting_autogen: phony CMakeFiles\sorting_autogen sorting_autogen\mocs_compilation.cpp


#############################################
# Custom command for sorting_autogen\EWIEGA46WW\qrc_qml.cpp

build sorting_autogen\EWIEGA46WW\qrc_qml.cpp | ${cmake_ninja_workdir}sorting_autogen\EWIEGA46WW\qrc_qml.cpp: CUSTOM_COMMAND E$:\software\sorting\qml.qrc CMakeFiles\sorting_autogen.dir\AutoRcc_qml_EWIEGA46WW_Info.json E$:\software\sorting\main.qml D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\bin\rcc.exe D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\bin\rcc.exe || sorting_autogen
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build-Debug && "C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json Debug"
  DESC = Automatic RCC for qml.qrc
  restat = 1


#############################################
# Custom command for CMakeFiles\sorting_autogen

build CMakeFiles\sorting_autogen sorting_autogen\mocs_compilation.cpp | ${cmake_ninja_workdir}CMakeFiles\sorting_autogen ${cmake_ninja_workdir}sorting_autogen\mocs_compilation.cpp: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build-Debug && "C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutogenInfo.json Debug"
  DESC = Automatic MOC and UIC for target sorting
  restat = 1

# =============================================================================
# Target aliases.

build sorting: phony sorting.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: E:/software/sorting/build-Debug

build all: phony sorting.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseArguments.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.29.3\CMakeCXXCompiler.cmake CMakeFiles\3.29.3\CMakeRCCompiler.cmake CMakeFiles\3.29.3\CMakeSystem.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake E$:\software\sorting\CMakeLists.txt E$:\software\sorting\qml.qrc
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseArguments.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.29.3\CMakeCXXCompiler.cmake CMakeFiles\3.29.3\CMakeRCCompiler.cmake CMakeFiles\3.29.3\CMakeSystem.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake E$:\software\sorting\CMakeLists.txt E$:\software\sorting\qml.qrc: phony


#############################################
# Clean additional files.

build CMakeFiles\clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles\clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
