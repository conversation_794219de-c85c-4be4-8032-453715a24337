CMakeFiles\sorting.dir\sorting.cpp.obj: E:\software\sorting\sorting.cpp \
  E:\software\sorting\sorting.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\QObject \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qobject.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qobjectdefs.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qnamespace.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qglobal.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\type_traits \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\c++config.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\os_defines.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\cpu_defines.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cstddef \
  D:\LLVM\lib\clang\12.0.0\include\stddef.h \
  D:\LLVM\lib\clang\12.0.0\include\__stddef_max_align_t.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\utility \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_relops.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_pair.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\move.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\concept_check.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\initializer_list \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\assert.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\crtdefs.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_mingw.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_mingw_mac.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_mingw_secapi.h \
  D:\LLVM\lib\clang\12.0.0\include\vadefs.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\vadefs.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sdks\_mingw_directx.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sdks\_mingw_ddk.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\stdlib.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cstdlib \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\stdlib.h \
  D:\LLVM\lib\clang\12.0.0\include\limits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\limits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sec_api\stdlib_s.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\malloc.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\std_abs.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qconfig.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qtcore-config.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qsystemdetection.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qprocessordetection.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qcompilerdetection.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\atomic \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\algorithm \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_algobase.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\functexcept.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\exception_defines.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\cpp_type_traits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ext\type_traits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ext\numeric_traits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_iterator_base_types.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_iterator_base_funcs.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\debug\assertions.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_iterator.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\ptr_traits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\debug\debug.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\predefined_ops.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_algo.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\algorithmfwd.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_heap.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_tempbuf.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_construct.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\new \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\exception \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\exception.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\exception_ptr.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\cxxabi_init_exception.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\typeinfo \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\hash_bytes.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\nested_exception.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ext\alloc_traits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\alloc_traits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\memoryfwd.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\uniform_int_dist.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\limits \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qtypeinfo.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qsysinfo.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qlogging.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qflags.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qatomic.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qbasicatomic.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qatomic_cxx11.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qgenericatomic.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\atomic_base.h \
  D:\LLVM\lib\clang\12.0.0\include\stdint.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\stdint.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\atomic_lockfree_defines.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qglobalstatic.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qnumeric.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qversiontagging.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qobjectdefs_impl.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qstring.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qchar.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qbytearray.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qrefcount.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qarraydata.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\string.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sec_api\string_s.h \
  D:\LLVM\lib\clang\12.0.0\include\stdarg.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\string \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stringfwd.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\char_traits.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\postypes.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cwchar \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\wchar.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_mingw_print_push.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_mingw_off_t.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_mingw_stat64.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\swprintf.inl \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sec_api\wchar_s.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_mingw_print_pop.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cstdint \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\allocator.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\c++allocator.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ext\new_allocator.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\localefwd.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\c++locale.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\clocale \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\locale.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\stdio.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sec_api\stdio_s.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\iosfwd \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cctype \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\ctype.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\ostream_insert.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\cxxabi_forced.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_function.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\backward\binders.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\range_access.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\basic_string.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ext\atomicity.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\gthr.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\gthr-default.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\pthread.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\errno.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sys\types.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\process.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\corecrt_startup.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\signal.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\pthread_signal.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sys\timeb.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\sec_api\sys\timeb_s.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\pthread_compat.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\pthread_unistd.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\atomic_word.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ext\string_conversions.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cstdio \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cerrno \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\functional_hash.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\basic_string.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\iterator \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ostream \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ios \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\ios_base.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\locale_classes.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\locale_classes.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\system_error \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\error_constants.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\stdexcept \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\streambuf \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\streambuf.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\basic_ios.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\locale_facets.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\cwctype \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\wctype.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\ctype_base.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\streambuf_iterator.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\i686-w64-mingw32\bits\ctype_inline.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\locale_facets.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\basic_ios.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\ostream.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\istream \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\istream.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stream_iterator.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qstringliteral.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qstringalgorithms.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qstringview.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qlist.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qalgorithms.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qiterator.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qhashfunctions.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qpair.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\numeric \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_numeric.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qvector.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\vector \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_uninitialized.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_vector.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_bvector.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\vector.tcc \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\list \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_list.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\allocated_ptr.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ext\aligned_buffer.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\list.tcc \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qbytearraylist.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qstringlist.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qregexp.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qstringmatcher.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qscopedpointer.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qmetatype.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qvarlengtharray.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qcontainerfwd.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\map \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_tree.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_map.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\tuple \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\array \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\uses_allocator.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\invoke.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\stl_multimap.h \
  D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\include\QtCore\qobject_impl.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\chrono \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ratio \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\ctime \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\time.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\_timeval.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\i686-w64-mingw32\include\pthread_time.h \
  D:\Qt\Qt5.13.2\Tools\mingw730_32\lib\gcc\i686-w64-mingw32\7.3.0\include\c++\bits\parse_numbers.h
