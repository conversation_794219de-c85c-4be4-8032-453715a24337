{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Program Files/CMake/bin/cmake.exe", "cpack": "C:/Program Files/CMake/bin/cpack.exe", "ctest": "C:/Program Files/CMake/bin/ctest.exe", "root": "C:/Program Files/CMake/share/cmake-3.29"}, "version": {"isDirty": false, "major": 3, "minor": 29, "patch": 3, "string": "3.29.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-30c9229f00dc18e0567f.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-f46d5ccbf2a66b2b9ac8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-68215a8dcb5c13c699ec.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-f46d5ccbf2a66b2b9ac8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-68215a8dcb5c13c699ec.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-30c9229f00dc18e0567f.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}