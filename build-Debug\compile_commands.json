[{"directory": "E:/software/sorting/build-Debug", "command": "C:\\PROGRA~2\\MICROS~2\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -IE:\\software\\sorting\\build-Debug -IE:\\software\\sorting -IE:\\software\\sorting\\build-Debug\\sorting_autogen\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtCore -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\.\\mkspecs\\win32-msvc -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQuick -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQml -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtNetwork -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtGui -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtANGLE /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 /FoCMakeFiles\\sorting.dir\\sorting_autogen\\mocs_compilation.cpp.obj /FdCMakeFiles\\sorting.dir\\ /FS -c E:\\software\\sorting\\build-Debug\\sorting_autogen\\mocs_compilation.cpp", "file": "E:\\software\\sorting\\build-Debug\\sorting_autogen\\mocs_compilation.cpp", "output": "CMakeFiles\\sorting.dir\\sorting_autogen\\mocs_compilation.cpp.obj"}, {"directory": "E:/software/sorting/build-Debug", "command": "C:\\PROGRA~2\\MICROS~2\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -IE:\\software\\sorting\\build-Debug -IE:\\software\\sorting -IE:\\software\\sorting\\build-Debug\\sorting_autogen\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtCore -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\.\\mkspecs\\win32-msvc -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQuick -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQml -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtNetwork -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtGui -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtANGLE /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 /FoCMakeFiles\\sorting.dir\\main.cpp.obj /FdCMakeFiles\\sorting.dir\\ /FS -c E:\\software\\sorting\\main.cpp", "file": "E:\\software\\sorting\\main.cpp", "output": "CMakeFiles\\sorting.dir\\main.cpp.obj"}, {"directory": "E:/software/sorting/build-Debug", "command": "C:\\PROGRA~2\\MICROS~2\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -IE:\\software\\sorting\\build-Debug -IE:\\software\\sorting -IE:\\software\\sorting\\build-Debug\\sorting_autogen\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtCore -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\.\\mkspecs\\win32-msvc -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQuick -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQml -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtNetwork -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtGui -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtANGLE /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 /FoCMakeFiles\\sorting.dir\\sorting.cpp.obj /FdCMakeFiles\\sorting.dir\\ /FS -c E:\\software\\sorting\\sorting.cpp", "file": "E:\\software\\sorting\\sorting.cpp", "output": "CMakeFiles\\sorting.dir\\sorting.cpp.obj"}, {"directory": "E:/software/sorting/build-Debug", "command": "C:\\PROGRA~2\\MICROS~2\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -IE:\\software\\sorting\\build-Debug -IE:\\software\\sorting -IE:\\software\\sorting\\build-Debug\\sorting_autogen\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtCore -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\.\\mkspecs\\win32-msvc -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQuick -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQml -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtNetwork -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtGui -ID:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtANGLE /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 /FoCMakeFiles\\sorting.dir\\sorting_autogen\\EWIEGA46WW\\qrc_qml.cpp.obj /FdCMakeFiles\\sorting.dir\\ /FS -c E:\\software\\sorting\\build-Debug\\sorting_autogen\\EWIEGA46WW\\qrc_qml.cpp", "file": "E:\\software\\sorting\\build-Debug\\sorting_autogen\\EWIEGA46WW\\qrc_qml.cpp", "output": "CMakeFiles\\sorting.dir\\sorting_autogen\\EWIEGA46WW\\qrc_qml.cpp.obj"}]