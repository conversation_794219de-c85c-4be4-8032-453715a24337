{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.5"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "sorting", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "sorting::@6890427a1f51a3e7e1df", "jsonFile": "target-sorting-Debug-7d7087e1ca469ba2b978.json", "name": "sorting", "projectIndex": 0}, {"directoryIndex": 0, "id": "sorting_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-sorting_autogen-Debug-ae94f2ba40dcdce29d24.json", "name": "sorting_autogen", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/software/sorting/build-Debug", "source": "E:/software/sorting"}, "version": {"major": 2, "minor": 7}}