/****************************************************************************
** Meta object code from reading C++ file 'sorting.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.13.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../sorting.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sorting.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.13.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SortingManager_t {
    QByteArrayData data[24];
    char stringdata0[302];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SortingManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SortingManager_t qt_meta_stringdata_SortingManager = {
    {
QT_MOC_LITERAL(0, 0, 14), // "SortingManager"
QT_MOC_LITERAL(1, 15, 12), // "messageAdded"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 7), // "message"
QT_MOC_LITERAL(4, 37, 9), // "timestamp"
QT_MOC_LITERAL(5, 47, 12), // "dataImported"
QT_MOC_LITERAL(6, 60, 11), // "recordCount"
QT_MOC_LITERAL(7, 72, 12), // "dataExported"
QT_MOC_LITERAL(8, 85, 8), // "filePath"
QT_MOC_LITERAL(9, 94, 11), // "recordAdded"
QT_MOC_LITERAL(10, 106, 6), // "record"
QT_MOC_LITERAL(11, 113, 16), // "selectImportFile"
QT_MOC_LITERAL(12, 130, 18), // "importDataFromFile"
QT_MOC_LITERAL(13, 149, 16), // "selectExportPath"
QT_MOC_LITERAL(14, 166, 15), // "exportDataToCsv"
QT_MOC_LITERAL(15, 182, 18), // "addDetectionRecord"
QT_MOC_LITERAL(16, 201, 7), // "barcode"
QT_MOC_LITERAL(17, 209, 6), // "result"
QT_MOC_LITERAL(18, 216, 6), // "defect"
QT_MOC_LITERAL(19, 223, 19), // "getDetectionRecords"
QT_MOC_LITERAL(20, 243, 12), // "clearRecords"
QT_MOC_LITERAL(21, 256, 19), // "getCurrentTimestamp"
QT_MOC_LITERAL(22, 276, 16), // "generateTestData"
QT_MOC_LITERAL(23, 293, 8) // "runTests"

    },
    "SortingManager\0messageAdded\0\0message\0"
    "timestamp\0dataImported\0recordCount\0"
    "dataExported\0filePath\0recordAdded\0"
    "record\0selectImportFile\0importDataFromFile\0"
    "selectExportPath\0exportDataToCsv\0"
    "addDetectionRecord\0barcode\0result\0"
    "defect\0getDetectionRecords\0clearRecords\0"
    "getCurrentTimestamp\0generateTestData\0"
    "runTests"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SortingManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   84,    2, 0x06 /* Public */,
       5,    1,   89,    2, 0x06 /* Public */,
       7,    1,   92,    2, 0x06 /* Public */,
       9,    1,   95,    2, 0x06 /* Public */,

 // methods: name, argc, parameters, tag, flags
      11,    0,   98,    2, 0x02 /* Public */,
      12,    1,   99,    2, 0x02 /* Public */,
      13,    0,  102,    2, 0x02 /* Public */,
      14,    1,  103,    2, 0x02 /* Public */,
      15,    3,  106,    2, 0x02 /* Public */,
      19,    0,  113,    2, 0x02 /* Public */,
      20,    0,  114,    2, 0x02 /* Public */,
      21,    0,  115,    2, 0x02 /* Public */,
      22,    0,  116,    2, 0x02 /* Public */,
      23,    0,  117,    2, 0x02 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::Int,    6,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QVariantMap,   10,

 // methods: parameters
    QMetaType::QString,
    QMetaType::Bool, QMetaType::QString,    8,
    QMetaType::QString,
    QMetaType::Bool, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QString,   16,   17,   18,
    QMetaType::QVariantList,
    QMetaType::Void,
    QMetaType::QString,
    QMetaType::Void,
    QMetaType::Bool,

       0        // eod
};

void SortingManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SortingManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->messageAdded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 1: _t->dataImported((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->dataExported((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->recordAdded((*reinterpret_cast< const QVariantMap(*)>(_a[1]))); break;
        case 4: { QString _r = _t->selectImportFile();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 5: { bool _r = _t->importDataFromFile((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 6: { QString _r = _t->selectExportPath();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 7: { bool _r = _t->exportDataToCsv((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 8: _t->addDetectionRecord((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 9: { QVariantList _r = _t->getDetectionRecords();
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 10: _t->clearRecords(); break;
        case 11: { QString _r = _t->getCurrentTimestamp();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 12: _t->generateTestData(); break;
        case 13: { bool _r = _t->runTests();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SortingManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SortingManager::messageAdded)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SortingManager::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SortingManager::dataImported)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SortingManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SortingManager::dataExported)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SortingManager::*)(const QVariantMap & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SortingManager::recordAdded)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SortingManager::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_SortingManager.data,
    qt_meta_data_SortingManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SortingManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SortingManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SortingManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SortingManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 14;
    }
    return _id;
}

// SIGNAL 0
void SortingManager::messageAdded(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void SortingManager::dataImported(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void SortingManager::dataExported(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void SortingManager::recordAdded(const QVariantMap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
