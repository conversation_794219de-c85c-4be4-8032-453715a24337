﻿#ifndef SORTING_H
#define SORTING_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QVariantList>
#include <QVariantMap>

struct DetectionRecord {
    QDateTime timestamp;
    QString barcode;
    QString detectionResult;
    QString defectInfo;

    DetectionRecord() = default;
    DetectionRecord(const QString& bc, const QString& result, const QString& defect)
        : timestamp(QDateTime::currentDateTime()), barcode(bc), detectionResult(result), defectInfo(defect) {}
};

class SortingManager : public QObject
{
    Q_OBJECT

public:
    explicit SortingManager(QObject *parent = nullptr);

    Q_INVOKABLE QString selectImportFile();
    Q_INVOKABLE bool importDataFromFile(const QString& filePath);
    Q_INVOKABLE QString selectExportPath();
    Q_INVOKABLE bool exportDataToCsv(const QString& filePath);
    Q_INVOKABLE void addDetectionRecord(const QString& barcode, const QString& result, const QString& defect);
    Q_INVOKABLE QVariantList getDetectionRecords() const;
    Q_INVOKABLE void clearRecords();
    Q_INVOKABLE QString getCurrentTimestamp() const;

    // 测试函数
    Q_INVOKABLE void generateTestData();
    Q_INVOKABLE bool runTests();

signals:
    void messageAdded(const QString& message, const QString& timestamp);
    void dataImported(int recordCount);
    void dataExported(const QString& filePath);
    void recordAdded(const QVariantMap& record);

private:
    QList<DetectionRecord> m_records;
    void addMessage(const QString& message);
    QVariantMap recordToVariantMap(const DetectionRecord& record) const;
};

#endif // SORTING_H
