# 质量检测与分拣系统使用示例

## 快速开始

### 1. 启动程序
运行编译后的程序：
```bash
.\build-Debug\sorting.exe
```

程序启动后会显示一个包含两个面板的窗口：
- 左侧：消息记录和控制按钮
- 右侧：检测记录表格和手动添加区域

### 2. 生成测试数据
点击"生成测试数据"按钮，系统会：
- 清空现有记录
- 自动生成10条测试记录
- 在消息记录中显示操作信息
- 在右侧表格中显示生成的记录

### 3. 导入数据测试
1. 使用提供的测试文件 `test_data.csv`
2. 点击"导入数据"按钮
3. 选择 `test_data.csv` 文件
4. 观察导入结果在消息记录中的显示
5. 检查右侧表格中的导入数据

### 4. 手动添加记录
在右侧底部的添加区域：
1. 输入条码：例如 "BC999"
2. 选择检测结果：合格 或 不合格
3. 输入缺陷信息：合格时填"无"，不合格时填具体缺陷
4. 点击"添加"按钮
5. 新记录会立即显示在表格顶部

### 5. 导出数据
1. 点击"数据导出"按钮
2. 选择保存位置和文件名
3. 系统会生成包含所有记录的CSV文件
4. 导出结果会在消息记录中显示

### 6. 运行系统测试
点击"运行测试"按钮，系统会自动执行：
- 添加记录功能测试
- 时间戳格式测试
- 导出功能测试
- 清空功能测试
- 测试结果会在消息记录中详细显示

## 实际使用场景

### 场景1：质量检测流水线
```
1. 产品通过检测设备
2. 系统自动获取条码和检测结果
3. 调用 addDetectionRecord() 添加记录
4. 实时显示在界面上
5. 定期导出数据进行分析
```

### 场景2：离线数据处理
```
1. 从其他系统导出检测数据
2. 使用"导入数据"功能加载
3. 在界面中查看和分析
4. 手动添加遗漏的记录
5. 重新导出完整数据
```

### 场景3：数据备份和恢复
```
1. 定期使用"数据导出"备份
2. 系统故障后使用"导入数据"恢复
3. 验证数据完整性
```

## 界面操作指南

### 消息记录区域
- 显示所有操作的时间戳消息
- 最新消息显示在顶部
- 包含操作类型和结果信息
- 自动滚动显示

### 检测记录表格
- **时间戳列**：显示记录创建时间
- **条码列**：显示产品条码
- **结果列**：绿色显示"合格"，红色显示"不合格"
- **缺陷信息列**：显示具体缺陷描述
- 支持滚动查看大量记录

### 控制按钮说明
- **导入数据**：从CSV文件导入检测记录
- **数据导出**：将当前记录导出为CSV文件
- **清空记录**：删除所有当前记录
- **生成测试数据**：创建示例数据用于测试
- **运行测试**：执行系统功能验证

## 数据格式要求

### CSV导入格式
```csv
时间戳,条码信息,检测结果,缺陷信息
2024-01-15 09:30:15,BC001,合格,无
2024-01-15 09:31:20,BC002,不合格,尺寸超差
```

### 字段要求
- **时间戳**：必须是 "yyyy-MM-dd hh:mm:ss" 格式
- **条码信息**：不能为空，建议使用唯一标识
- **检测结果**：只能是"合格"或"不合格"
- **缺陷信息**：合格时建议填"无"，不合格时填具体缺陷

## 常见问题

### Q: 导入文件时提示失败？
A: 检查文件格式是否正确，确保使用UTF-8编码，时间戳格式正确

### Q: 导出的文件在哪里？
A: 在您选择的保存路径，默认建议保存在文档文件夹

### Q: 如何清空所有数据？
A: 点击"清空记录"按钮，会删除内存中的所有记录

### Q: 程序关闭后数据会丢失吗？
A: 是的，数据只保存在内存中，需要手动导出保存

### Q: 可以同时处理多少条记录？
A: 理论上没有限制，但建议单次处理不超过10000条以保证性能

## 扩展功能建议

1. **数据库支持**：将数据持久化到数据库
2. **网络功能**：支持远程数据同步
3. **统计分析**：添加合格率统计图表
4. **报警功能**：不合格率超标时报警
5. **用户权限**：添加用户登录和权限管理
