{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/.qtc/package-manager/auto-setup.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeJOMFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"}, {"path": "qml.qrc"}], "kind": "cmakeFiles", "paths": {"build": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "source": "E:/software/sorting"}, "version": {"major": 1, "minor": 0}}