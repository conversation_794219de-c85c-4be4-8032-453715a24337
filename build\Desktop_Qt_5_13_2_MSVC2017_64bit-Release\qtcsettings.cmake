# This file is managed by Qt Creator, do not edit!

set("CMAKE_C_COMPILER_TARGET" "i686-w64-mingw32" CACHE "STRING" "" FORCE)
set("QT_QMAKE_EXECUTABLE" "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin/qmake.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PROJECT_INCLUDE_BEFORE" "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)
set("CMAKE_C_COMPILER" "D:/LLVM/bin/clang.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PREFIX_PATH" "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64" CACHE "PATH" "" FORCE)
set("CMAKE_CXX_COMPILER" "D:/LLVM/bin/clang++.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_BUILD_TYPE" "Release" CACHE "STRING" "" FORCE)
set("CMAKE_SYSROOT" "D:/Qt/Qt5.13.2/Tools/mingw730_32" CACHE "PATH" "" FORCE)
set("CMAKE_CXX_COMPILER_TARGET" "i686-w64-mingw32" CACHE "STRING" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "" CACHE "STRING" "" FORCE)
set("CMAKE_GENERATOR" "NMake Makefiles JOM" CACHE "STRING" "" FORCE)