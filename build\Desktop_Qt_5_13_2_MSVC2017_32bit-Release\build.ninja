# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.29

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: sorting
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = E$:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\
# =============================================================================
# Object build statements for EXECUTABLE target sorting


#############################################
# Order-only phony target for sorting

build cmake_object_order_depends_target_sorting: phony || sorting_autogen sorting_autogen\EWIEGA46WW\qrc_qml.cpp

build CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj: CXX_COMPILER__sorting_unscanned_Release E$:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\sorting_autogen\mocs_compilation.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG
  INCLUDES = -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release -IE:\software\sorting -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtANGLE
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir\sorting_autogen
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb

build CMakeFiles\sorting.dir\main.cpp.obj: CXX_COMPILER__sorting_unscanned_Release E$:\software\sorting\main.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG
  INCLUDES = -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release -IE:\software\sorting -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtANGLE
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb

build CMakeFiles\sorting.dir\sorting.cpp.obj: CXX_COMPILER__sorting_unscanned_Release E$:\software\sorting\sorting.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG
  INCLUDES = -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release -IE:\software\sorting -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtANGLE
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb

build CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj: CXX_COMPILER__sorting_unscanned_Release E$:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\sorting_autogen\EWIEGA46WW\qrc_qml.cpp || cmake_object_order_depends_target_sorting
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG
  INCLUDES = -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release -IE:\software\sorting -IE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\sorting_autogen\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtCore -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\.\mkspecs\win32-msvc -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQuick -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtQml -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtNetwork -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtGui -ID:\Qt\Qt5.13.2\5.13.2\msvc2017\include\QtANGLE
  OBJECT_DIR = CMakeFiles\sorting.dir
  OBJECT_FILE_DIR = CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_PDB = sorting.pdb


# =============================================================================
# Link build statements for EXECUTABLE target sorting


#############################################
# Link the executable sorting.exe

build sorting.exe: CXX_EXECUTABLE_LINKER__sorting_Release CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj CMakeFiles\sorting.dir\main.cpp.obj CMakeFiles\sorting.dir\sorting.cpp.obj CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj | D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Quick.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Qml.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Network.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Gui.lib D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Core.lib || sorting_autogen
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG
  LINK_FLAGS = /machine:X86 /INCREMENTAL:NO /subsystem:console
  LINK_LIBRARIES = D:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Quick.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Qml.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Network.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Gui.lib  D:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\Qt5Core.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\sorting.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\sorting.dir\
  TARGET_FILE = sorting.exe
  TARGET_IMPLIB = sorting.lib
  TARGET_PDB = sorting.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release && "C:\Program Files\CMake\bin\cmake-gui.exe" -SE:\software\sorting -BE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SE:\software\sorting -BE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Utility command for sorting_autogen

build sorting_autogen: phony CMakeFiles\sorting_autogen sorting_autogen\mocs_compilation.cpp


#############################################
# Custom command for sorting_autogen\EWIEGA46WW\qrc_qml.cpp

build sorting_autogen\EWIEGA46WW\qrc_qml.cpp | ${cmake_ninja_workdir}sorting_autogen\EWIEGA46WW\qrc_qml.cpp: CUSTOM_COMMAND E$:\software\sorting\qml.qrc CMakeFiles\sorting_autogen.dir\AutoRcc_qml_EWIEGA46WW_Info.json E$:\software\sorting\main.qml D$:\Qt\Qt5.13.2\5.13.2\msvc2017\bin\rcc.exe D$:\Qt\Qt5.13.2\5.13.2\msvc2017\bin\rcc.exe || sorting_autogen
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release && "C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json Release"
  DESC = Automatic RCC for qml.qrc
  restat = 1


#############################################
# Custom command for CMakeFiles\sorting_autogen

build CMakeFiles\sorting_autogen sorting_autogen\mocs_compilation.cpp | ${cmake_ninja_workdir}CMakeFiles\sorting_autogen ${cmake_ninja_workdir}sorting_autogen\mocs_compilation.cpp: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Release && "C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release/CMakeFiles/sorting_autogen.dir/AutogenInfo.json Release"
  DESC = Automatic MOC and UIC for target sorting
  restat = 1

# =============================================================================
# Target aliases.

build sorting: phony sorting.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release

build all: phony sorting.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc\package-manager\auto-setup.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeNinjaFindMake.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseArguments.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystem.cmake.in C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.29.3\CMakeCXXCompiler.cmake CMakeFiles\3.29.3\CMakeRCCompiler.cmake CMakeFiles\3.29.3\CMakeSystem.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreMacros.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5\Qt5Config.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5\Qt5ConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5\Qt5ModuleLocation.cmake E$:\software\sorting\CMakeLists.txt E$:\software\sorting\qml.qrc
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc\package-manager\auto-setup.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeNinjaFindMake.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseArguments.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystem.cmake.in C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake C$:\Program$ Files\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.29.3\CMakeCXXCompiler.cmake CMakeFiles\3.29.3\CMakeRCCompiler.cmake CMakeFiles\3.29.3\CMakeSystem.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Core\Qt5CoreMacros.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5\Qt5Config.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5\Qt5ConfigVersion.cmake D$:\Qt\Qt5.13.2\5.13.2\msvc2017\lib\cmake\Qt5\Qt5ModuleLocation.cmake E$:\software\sorting\CMakeLists.txt E$:\software\sorting\qml.qrc: phony


#############################################
# Clean additional files.

build CMakeFiles\clean.additional: CLEAN_ADDITIONAL
  CONFIG = Release


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles\clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
