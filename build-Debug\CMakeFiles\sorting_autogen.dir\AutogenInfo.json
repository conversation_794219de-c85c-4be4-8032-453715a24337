{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "E:/software/sorting/build-Debug/sorting_autogen", "CMAKE_BINARY_DIR": "E:/software/sorting/build-Debug", "CMAKE_CURRENT_BINARY_DIR": "E:/software/sorting/build-Debug", "CMAKE_CURRENT_SOURCE_DIR": "E:/software/sorting", "CMAKE_EXECUTABLE": "C:/Program Files/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["E:/software/sorting/CMakeLists.txt", "E:/software/sorting/build-Debug/CMakeFiles/3.29.3/CMakeSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Initialize.cmake", "E:/software/sorting/build-Debug/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeGenericSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/WindowsPaths.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-MSVC.cmake", "E:/software/sorting/build-Debug/CMakeFiles/3.29.3/CMakeRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "E:/software/sorting/qml.qrc"], "CMAKE_SOURCE_DIR": "E:/software/sorting", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["E:/software/sorting/sorting.h", "MU", "EWIEGA46WW/moc_sorting.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "E:/software/sorting/build-Debug/sorting_autogen/include", "MOC_COMPILATION_FILE": "E:/software/sorting/build-Debug/sorting_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_QML_DEBUG", "QT_QML_LIB", "QT_QUICK_LIB", "WIN32"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["E:/software/sorting/build-Debug", "E:/software/sorting", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtANGLE"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 13, "SETTINGS_FILE": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutogenUsed.txt", "SOURCES": [["E:/software/sorting/main.cpp", "MU", null], ["E:/software/sorting/sorting.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}