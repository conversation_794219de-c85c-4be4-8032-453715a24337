{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "E:/software/sorting/build-Debug/sorting_autogen", "CMAKE_BINARY_DIR": "E:/software/sorting/build-Debug", "CMAKE_CURRENT_BINARY_DIR": "E:/software/sorting/build-Debug", "CMAKE_CURRENT_SOURCE_DIR": "E:/software/sorting", "CMAKE_EXECUTABLE": "C:/Program Files/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["E:/software/sorting/CMakeLists.txt", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystem.cmake.in", "E:/software/sorting/build-Debug/CMakeFiles/3.29.3/CMakeSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Initialize.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Determine-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCompilerIdDetection.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ADSP-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMCC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/AppleClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Borland-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Cray-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CrayClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Embarcadero-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Fujitsu-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GHS-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/HP-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IAR-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Intel-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVHPC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVIDIA-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OrangeC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PGI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PathScale-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SCO-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TIClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Tasking-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Watcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XL-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CompilerId/VS-10.vcxproj.in", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeFindBinUtils.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in", "E:/software/sorting/build-Debug/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeGenericSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/WindowsPaths.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-MSVC.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCCompiler.cmake.in", "E:/software/sorting/build-Debug/CMakeFiles/3.29.3/CMakeRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitIncludeInfo.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitLinkInfo.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseLibraryArchitecture.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompileFeatures.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/FeatureTesting.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in", "E:/software/sorting/build-Debug/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake", "E:/software/sorting/qml.qrc"], "CMAKE_SOURCE_DIR": "E:/software/sorting", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["E:/software/sorting/sorting.h", "MU", "EWIEGA46WW/moc_sorting.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "E:/software/sorting/build-Debug/sorting_autogen/include", "INCLUDE_DIR_Debug": "E:/software/sorting/build-Debug/sorting_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "E:/software/sorting/build-Debug/sorting_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "E:/software/sorting/build-Debug/sorting_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "E:/software/sorting/build-Debug/sorting_autogen/include_Release", "MOC_COMPILATION_FILE": "E:/software/sorting/build-Debug/sorting_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "E:/software/sorting/build-Debug/sorting_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "E:/software/sorting/build-Debug/sorting_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "E:/software/sorting/build-Debug/sorting_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "E:/software/sorting/build-Debug/sorting_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_QML_DEBUG", "QT_QML_LIB", "QT_QUICK_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEFINITIONS_MinSizeRel": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_QML_LIB", "QT_QUICK_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEFINITIONS_RelWithDebInfo": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_QML_DEBUG", "QT_QML_LIB", "QT_QUICK_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEFINITIONS_Release": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_QML_LIB", "QT_QUICK_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["E:/software/sorting/build-Debug", "E:/software/sorting", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtWidgets"], "MOC_INCLUDES_MinSizeRel": ["E:/software/sorting/build-Debug", "E:/software/sorting", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtWidgets"], "MOC_INCLUDES_RelWithDebInfo": ["E:/software/sorting/build-Debug", "E:/software/sorting", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtWidgets"], "MOC_INCLUDES_Release": ["E:/software/sorting/build-Debug", "E:/software/sorting", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtWidgets"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 8, "PARSE_CACHE_FILE": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 13, "SETTINGS_FILE": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["E:/software/sorting/main.cpp", "MU", null], ["E:/software/sorting/sorting.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}