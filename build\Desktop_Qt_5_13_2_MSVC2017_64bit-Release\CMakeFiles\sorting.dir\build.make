# CMAKE generated file: DO NOT EDIT!
# Generated by "NMake Makefiles JOM" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

!IF "$(OS)" == "Windows_NT"
NULL=
!ELSE
NULL=nul
!ENDIF
SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\software\sorting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release

# Include any dependencies generated for this target.
include CMakeFiles\sorting.dir\depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles\sorting.dir\compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles\sorting.dir\progress.make

# Include the compile flags for this target's objects.
include CMakeFiles\sorting.dir\flags.make

sorting_autogen\EWIEGA46WW\qrc_qml.cpp: E:\software\sorting\qml.qrc
sorting_autogen\EWIEGA46WW\qrc_qml.cpp: CMakeFiles\sorting_autogen.dir\AutoRcc_qml_EWIEGA46WW_Info.json
sorting_autogen\EWIEGA46WW\qrc_qml.cpp: E:\software\sorting\main.qml
sorting_autogen\EWIEGA46WW\qrc_qml.cpp: D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\bin\rcc.exe
sorting_autogen\EWIEGA46WW\qrc_qml.cpp: D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\bin\rcc.exe
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic RCC for qml.qrc"
	echo >nul && "C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json Release

CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj: CMakeFiles\sorting.dir\flags.make
CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj: CMakeFiles\sorting.dir\includes_CXX.rsp
CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj: sorting_autogen\mocs_compilation.cpp
CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj: CMakeFiles\sorting.dir\compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/sorting.dir/sorting_autogen/mocs_compilation.cpp.obj"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj -MF CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj.d -o CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj -c E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\sorting_autogen\mocs_compilation.cpp

CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sorting.dir/sorting_autogen/mocs_compilation.cpp.i"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\sorting_autogen\mocs_compilation.cpp > CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.i

CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sorting.dir/sorting_autogen/mocs_compilation.cpp.s"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\sorting_autogen\mocs_compilation.cpp -o CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.s

CMakeFiles\sorting.dir\main.cpp.obj: CMakeFiles\sorting.dir\flags.make
CMakeFiles\sorting.dir\main.cpp.obj: CMakeFiles\sorting.dir\includes_CXX.rsp
CMakeFiles\sorting.dir\main.cpp.obj: E:\software\sorting\main.cpp
CMakeFiles\sorting.dir\main.cpp.obj: CMakeFiles\sorting.dir\compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/sorting.dir/main.cpp.obj"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles\sorting.dir\main.cpp.obj -MF CMakeFiles\sorting.dir\main.cpp.obj.d -o CMakeFiles\sorting.dir\main.cpp.obj -c E:\software\sorting\main.cpp

CMakeFiles\sorting.dir\main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sorting.dir/main.cpp.i"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\software\sorting\main.cpp > CMakeFiles\sorting.dir\main.cpp.i

CMakeFiles\sorting.dir\main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sorting.dir/main.cpp.s"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\software\sorting\main.cpp -o CMakeFiles\sorting.dir\main.cpp.s

CMakeFiles\sorting.dir\sorting.cpp.obj: CMakeFiles\sorting.dir\flags.make
CMakeFiles\sorting.dir\sorting.cpp.obj: CMakeFiles\sorting.dir\includes_CXX.rsp
CMakeFiles\sorting.dir\sorting.cpp.obj: E:\software\sorting\sorting.cpp
CMakeFiles\sorting.dir\sorting.cpp.obj: CMakeFiles\sorting.dir\compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/sorting.dir/sorting.cpp.obj"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles\sorting.dir\sorting.cpp.obj -MF CMakeFiles\sorting.dir\sorting.cpp.obj.d -o CMakeFiles\sorting.dir\sorting.cpp.obj -c E:\software\sorting\sorting.cpp

CMakeFiles\sorting.dir\sorting.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sorting.dir/sorting.cpp.i"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\software\sorting\sorting.cpp > CMakeFiles\sorting.dir\sorting.cpp.i

CMakeFiles\sorting.dir\sorting.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sorting.dir/sorting.cpp.s"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\software\sorting\sorting.cpp -o CMakeFiles\sorting.dir\sorting.cpp.s

CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj: CMakeFiles\sorting.dir\flags.make
CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj: CMakeFiles\sorting.dir\includes_CXX.rsp
CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj: sorting_autogen\EWIEGA46WW\qrc_qml.cpp
CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj: CMakeFiles\sorting.dir\compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/sorting.dir/sorting_autogen/EWIEGA46WW/qrc_qml.cpp.obj"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj -MF CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj.d -o CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj -c E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\sorting_autogen\EWIEGA46WW\qrc_qml.cpp

CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sorting.dir/sorting_autogen/EWIEGA46WW/qrc_qml.cpp.i"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\sorting_autogen\EWIEGA46WW\qrc_qml.cpp > CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.i

CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sorting.dir/sorting_autogen/EWIEGA46WW/qrc_qml.cpp.s"
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\sorting_autogen\EWIEGA46WW\qrc_qml.cpp -o CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.s

# Object files for target sorting
sorting_OBJECTS = \
"CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj" \
"CMakeFiles\sorting.dir\main.cpp.obj" \
"CMakeFiles\sorting.dir\sorting.cpp.obj" \
"CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj"

# External object files for target sorting
sorting_EXTERNAL_OBJECTS =

sorting.exe: CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj
sorting.exe: CMakeFiles\sorting.dir\main.cpp.obj
sorting.exe: CMakeFiles\sorting.dir\sorting.cpp.obj
sorting.exe: CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj
sorting.exe: CMakeFiles\sorting.dir\build.make
sorting.exe: D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Quick.lib
sorting.exe: D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Qml.lib
sorting.exe: D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Network.lib
sorting.exe: D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Gui.lib
sorting.exe: D:\Qt\Qt5.13.2\5.13.2\msvc2017_64\lib\Qt5Core.lib
sorting.exe: CMakeFiles\sorting.dir\linkLibs.rsp
sorting.exe: CMakeFiles\sorting.dir\objects1.rsp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable sorting.exe"
	"C:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\sorting.dir/objects.a
	D:\LLVM\bin\llvm-ar.exe qc CMakeFiles\sorting.dir/objects.a @CMakeFiles\sorting.dir\objects1.rsp
	D:\LLVM\bin\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\sorting.dir/objects.a -Wl,--no-whole-archive -o sorting.exe -Wl,--out-implib,libsorting.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\sorting.dir\linkLibs.rsp

# Rule to build all files generated by this target.
CMakeFiles\sorting.dir\build: sorting.exe
.PHONY : CMakeFiles\sorting.dir\build

CMakeFiles\sorting.dir\clean:
	$(CMAKE_COMMAND) -P CMakeFiles\sorting.dir\cmake_clean.cmake
.PHONY : CMakeFiles\sorting.dir\clean

CMakeFiles\sorting.dir\depend: sorting_autogen\EWIEGA46WW\qrc_qml.cpp
	$(CMAKE_COMMAND) -E cmake_depends "NMake Makefiles JOM" E:\software\sorting E:\software\sorting E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles\sorting.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles\sorting.dir\depend

