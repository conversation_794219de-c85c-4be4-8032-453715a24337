{"artifacts": [{"path": "sorting.exe"}, {"path": "sorting.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_property", "_populate_Quick_target_properties", "find_package", "_populate_Qml_target_properties", "target_compile_definitions"], "files": ["CMakeLists.txt", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 19, "parent": 0}, {"command": 1, "file": 0, "line": 25, "parent": 0}, {"command": 4, "file": 0, "line": 14, "parent": 0}, {"file": 2, "parent": 3}, {"command": 4, "file": 2, "line": 28, "parent": 4}, {"file": 1, "parent": 5}, {"command": 3, "file": 1, "line": 167, "parent": 6}, {"command": 2, "file": 1, "line": 45, "parent": 7}, {"command": 4, "file": 1, "line": 106, "parent": 6}, {"file": 3, "parent": 9}, {"command": 5, "file": 3, "line": 167, "parent": 10}, {"command": 2, "file": 3, "line": 45, "parent": 11}, {"command": 6, "file": 0, "line": 23, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++17"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 13, "define": "QT_QML_DEBUG"}, {"backtrace": 2, "define": "QT_QML_LIB"}, {"backtrace": 2, "define": "QT_QUICK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "E:/software/sorting/build-Debug"}, {"path": "E:/software/sorting"}, {"backtrace": 0, "path": "E:/software/sorting/build-Debug/sorting_autogen/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQuick"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQml"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0, 1, 4, 5]}], "dependencies": [{"backtrace": 0, "id": "sorting_autogen::@6890427a1f51a3e7e1df"}], "id": "sorting::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\lib\\Qt5Quickd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\lib\\Qt5Qmld.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\lib\\Qt5Networkd.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "sorting", "nameOnDisk": "sorting.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 4, 5]}, {"name": "", "sourceIndexes": [2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3]}, {"name": "CMake Rules", "sourceIndexes": [6]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build-Debug/sorting_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "qml.qrc", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "sorting.h", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "sorting.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build-Debug/sorting_autogen/EWIEGA46WW/qrc_qml.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build-Debug/sorting_autogen/EWIEGA46WW/qrc_qml.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}