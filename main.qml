import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Window {
    id: mainWindow
    visible: true
    width: 1200
    height: 800
    title: "质量检测与分拣系统"

    property color primaryColor: "#2196F3"
    property color accentColor: "#FF9800"
    property color backgroundColor: "#FAFAFA"
    property color surfaceColor: "#FFFFFF"

    // Connect to C++ signals
    Connections {
        target: sortingManager
        onMessageAdded: {
            messageModel.insert(0, {
                "message": message,
                "time": timestamp
            })
            // Auto scroll to top
            if (messageList.count > 100) {
                messageModel.remove(100, messageModel.count - 100)
            }
        }
        onDataImported: {
            recordModel.clear()
            var records = sortingManager.getDetectionRecords()
            for (var i = 0; i < records.length; i++) {
                recordModel.append(records[i])
            }
            statusBar.showMessage("已导入 " + records.length + " 条记录", 3000)
        }
        onRecordAdded: {
            recordModel.insert(0, record)
        }
        onDataExported: {
            statusBar.showMessage("数据导出成功", 3000)
        }
    }

    Rectangle {
        anchors.fill: parent
        color: backgroundColor

        RowLayout {
            anchors.fill: parent
            anchors.margins: 10
            spacing: 10

            // Left Panel - Message Log
            Rectangle {
                Layout.fillHeight: true
                Layout.preferredWidth: 400
                color: surfaceColor
                radius: 8
                border.color: "#E0E0E0"
                border.width: 1

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 8
                    spacing: 0

                    Rectangle {
                        Layout.fillWidth: true
                        height: 40
                        color: primaryColor
                        radius: 6

                        Text {
                            anchors.centerIn: parent
                            text: "消息记录"
                            color: "white"
                            font.bold: true
                            font.pixelSize: 16
                        }
                    }

                    ListView {
                        id: messageList
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.topMargin: 8
                        clip: true
                        spacing: 4
                        verticalLayoutDirection: ListView.BottomToTop

                        model: ListModel {
                            id: messageModel
                            Component.onCompleted: {
                                append({
                                    "message": "系统初始化成功",
                                    "time": Qt.formatTime(new Date(), "hh:mm:ss")
                                })
                            }
                        }

                        delegate: Rectangle {
                            width: messageList.width
                            height: Math.max(40, messageText.contentHeight + 16)
                            color: "#F5F5F5"
                            border.color: "#E0E0E0"
                            radius: 6

                            Rectangle {
                                width: 4
                                height: parent.height
                                color: primaryColor
                                radius: 2
                            }

                            Text {
                                id: messageText
                                anchors.left: parent.left
                                anchors.right: timeText.left
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                anchors.leftMargin: 12
                                anchors.rightMargin: 8
                                anchors.topMargin: 8
                                anchors.bottomMargin: 8
                                text: message
                                color: "#333333"
                                wrapMode: Text.WordWrap
                                font.pixelSize: 12
                                verticalAlignment: Text.AlignVCenter
                            }

                            Text {
                                id: timeText
                                anchors.right: parent.right
                                anchors.top: parent.top
                                anchors.margins: 8
                                text: time
                                color: "#666666"
                                font.pixelSize: 10
                                font.family: "Consolas, monospace"
                            }
                        }
                    }

                    // Control Buttons Area
                    Rectangle {
                        Layout.fillWidth: true
                        height: 160
                        color: "#F8F9FA"
                        radius: 6
                        border.color: "#E0E0E0"
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 8

                            Text {
                                text: "控制面板"
                                font.bold: true
                                font.pixelSize: 14
                                color: "#333333"
                            }

                            // First row buttons
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Button {
                                    text: "导入数据"
                                    Layout.preferredWidth: 90
                                    Layout.preferredHeight: 32
                                    onClicked: {
                                        var filePath = sortingManager.selectImportFile()
                                        if (filePath !== "") {
                                            sortingManager.importDataFromFile(filePath)
                                        }
                                    }

                                    background: Rectangle {
                                        color: parent.pressed ? "#1976D2" : primaryColor
                                        radius: 4
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }

                                Button {
                                    text: "导出数据"
                                    Layout.preferredWidth: 90
                                    Layout.preferredHeight: 32
                                    onClicked: {
                                        var filePath = sortingManager.selectExportPath()
                                        if (filePath !== "") {
                                            sortingManager.exportDataToCsv(filePath)
                                        }
                                    }

                                    background: Rectangle {
                                        color: parent.pressed ? "#E65100" : accentColor
                                        radius: 4
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }
                            }

                            // Second row buttons
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Button {
                                    text: "清空记录"
                                    Layout.preferredWidth: 90
                                    Layout.preferredHeight: 32
                                    onClicked: {
                                        sortingManager.clearRecords()
                                        recordModel.clear()
                                    }

                                    background: Rectangle {
                                        color: parent.pressed ? "#C62828" : "#F44336"
                                        radius: 4
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }

                                Button {
                                    text: "测试数据"
                                    Layout.preferredWidth: 90
                                    Layout.preferredHeight: 32
                                    onClicked: {
                                        sortingManager.generateTestData()
                                    }

                                    background: Rectangle {
                                        color: parent.pressed ? "#388E3C" : "#4CAF50"
                                        radius: 4
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }
                            }

                            // Third row button
                            Button {
                                text: "运行测试"
                                Layout.preferredWidth: 120
                                Layout.preferredHeight: 32
                                onClicked: {
                                    sortingManager.runTests()
                                }

                                background: Rectangle {
                                    color: parent.pressed ? "#7B1FA2" : "#9C27B0"
                                    radius: 4
                                }

                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 11
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                        }
                    }
                }
            }

            // Right Panel - Detection Records
            Rectangle {
                Layout.fillHeight: true
                Layout.fillWidth: true
                color: surfaceColor
                radius: 8
                border.color: "#E0E0E0"
                border.width: 1

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 8
                    spacing: 0

                    Rectangle {
                        Layout.fillWidth: true
                        height: 40
                        color: primaryColor
                        radius: 6

                        Text {
                            anchors.centerIn: parent
                            text: "检测记录"
                            color: "white"
                            font.bold: true
                            font.pixelSize: 16
                        }
                    }

                    // Table Header
                    Rectangle {
                        Layout.fillWidth: true
                        height: 40
                        color: "#F5F5F5"
                        border.color: "#E0E0E0"
                        border.width: 1

                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 8
                            spacing: 1

                            Text {
                                Layout.preferredWidth: 140
                                text: "时间戳"
                                font.bold: true
                                font.pixelSize: 12
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                            Rectangle { width: 1; Layout.fillHeight: true; color: "#D0D0D0" }
                            Text {
                                Layout.preferredWidth: 100
                                text: "条码"
                                font.bold: true
                                font.pixelSize: 12
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                            Rectangle { width: 1; Layout.fillHeight: true; color: "#D0D0D0" }
                            Text {
                                Layout.preferredWidth: 80
                                text: "结果"
                                font.bold: true
                                font.pixelSize: 12
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                            Rectangle { width: 1; Layout.fillHeight: true; color: "#D0D0D0" }
                            Text {
                                Layout.fillWidth: true
                                text: "缺陷信息"
                                font.bold: true
                                font.pixelSize: 12
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                    }

                    ListView {
                        id: recordList
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.topMargin: 8
                        clip: true
                        spacing: 2

                        model: ListModel {
                            id: recordModel
                        }

                        delegate: Rectangle {
                            width: recordList.width
                            height: 36
                            color: index % 2 === 0 ? "#FFFFFF" : "#F8F9FA"
                            border.color: "#E0E0E0"
                            radius: 4

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                spacing: 1

                                Text {
                                    Layout.preferredWidth: 140
                                    text: timestamp
                                    font.pixelSize: 10
                                    font.family: "Consolas, monospace"
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    color: "#555555"
                                }
                                Rectangle { width: 1; Layout.fillHeight: true; color: "#E0E0E0" }
                                Text {
                                    Layout.preferredWidth: 100
                                    text: barcode
                                    font.pixelSize: 11
                                    font.bold: true
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    color: "#333333"
                                }
                                Rectangle { width: 1; Layout.fillHeight: true; color: "#E0E0E0" }
                                Rectangle {
                                    Layout.preferredWidth: 80
                                    Layout.fillHeight: true
                                    color: detectionResult === "合格" ? "#E8F5E8" : "#FFEBEE"
                                    radius: 3

                                    Text {
                                        anchors.centerIn: parent
                                        text: detectionResult
                                        font.pixelSize: 11
                                        color: detectionResult === "合格" ? "#2E7D32" : "#C62828"
                                        font.bold: true
                                    }
                                }
                                Rectangle { width: 1; Layout.fillHeight: true; color: "#E0E0E0" }
                                Text {
                                    Layout.fillWidth: true
                                    text: defectInfo
                                    font.pixelSize: 11
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                    elide: Text.ElideRight
                                    color: "#555555"
                                }
                            }
                        }
                    }

                    // Manual Add Record Area
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#F8F9FA"
                        border.color: "#E0E0E0"
                        border.width: 1
                        radius: 6

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 8

                            Text {
                                text: "添加新记录"
                                font.bold: true
                                font.pixelSize: 12
                                color: "#333333"
                            }

                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                TextField {
                                    id: barcodeInput
                                    Layout.preferredWidth: 100
                                    placeholderText: "条码"
                                    font.pixelSize: 11

                                    background: Rectangle {
                                        color: "#FFFFFF"
                                        border.color: "#D0D0D0"
                                        border.width: 1
                                        radius: 4
                                    }
                                }

                                ComboBox {
                                    id: resultCombo
                                    Layout.preferredWidth: 100
                                    model: ["合格", "不合格"]
                                    font.pixelSize: 11

                                    background: Rectangle {
                                        color: "#FFFFFF"
                                        border.color: "#D0D0D0"
                                        border.width: 1
                                        radius: 4
                                    }
                                }

                                TextField {
                                    id: defectInput
                                    Layout.fillWidth: true
                                    placeholderText: "缺陷信息（合格时填'无'）"
                                    font.pixelSize: 11

                                    background: Rectangle {
                                        color: "#FFFFFF"
                                        border.color: "#D0D0D0"
                                        border.width: 1
                                        radius: 4
                                    }
                                }

                                Button {
                                    text: "添加"
                                    Layout.preferredWidth: 60
                                    Layout.preferredHeight: 32
                                    onClicked: {
                                        if (barcodeInput.text !== "") {
                                            var defect = defectInput.text === "" ? "无" : defectInput.text
                                            sortingManager.addDetectionRecord(
                                                barcodeInput.text,
                                                resultCombo.currentText,
                                                defect
                                            )
                                            barcodeInput.text = ""
                                            defectInput.text = ""
                                        }
                                    }

                                    background: Rectangle {
                                        color: parent.pressed ? "#388E3C" : "#4CAF50"
                                        radius: 4
                                    }

                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        font.pixelSize: 11
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
