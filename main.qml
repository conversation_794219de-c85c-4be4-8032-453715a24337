import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Window {
    visible: true
    width: 640
    height: 480
    title: qsTr("Sorting")
    ColumnLayout {
        anchors.fill: parent
        spacing: 0

        Rectangle {
            Layout.fillWidth: true
            height: 25
            color: "#e0e0e0"

            Text {
                anchors.centerIn: parent
                text: "消息记录"
                font.bold: true
            }
        }

        ListView {
            id: messageList
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.margins: 5
            clip: true
            spacing: 4
            verticalLayoutDirection: ListView.BottomToTop

            model: ListModel {
                id: messageModel
                Component.onCompleted: {
                    insert(0, {
                               "message": "消息记录区域初始化成功",
                               "time": new Date().toLocaleTimeString()
                           })
                }
            }
            delegate: Rectangle {
                width: messageList.width
                height: messageText.contentHeight + 10
                color: "#f8f8f8"
                border.color: "#e0e0e0"
                radius: 4

                Text {
                    id: messageText
                    anchors.fill: parent
                    anchors.margins: 5
                    text: message
                    color: "#333333"
                    wrapMode: Text.WordWrap
                    font.pixelSize: 12
                }
            }
        }
        Rectangle {
            Layout.fillWidth: true
            height: 40
            color: "#f0f0f0"
            border.color: "#e0e0e0"

            RowLayout {
                anchors.fill: parent
                anchors.margins: 5
                spacing: 10

                Button {
                    text: qsTr("导入数据")
                    Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                    Layout.minimumWidth: 100
                    onClicked: {
                        messageModel.insert(0, {
                                                "message": "点击了导入数据按钮",
                                                "time": new Date().toLocaleTimeString()
                                            })
                    }
                }

                Button {
                    text: qsTr("数据导出")
                    Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                    Layout.minimumWidth: 100
                    onClicked: {
                        messageModel.insert(0, {
                                                "message": "点击了数据导出",
                                                "time": new Date().toLocaleTimeString()
                                            })
                    }
                }
            }
        }
    }
}
