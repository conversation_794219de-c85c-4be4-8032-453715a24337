import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Window {
    visible: true
    width: 900
    height: 600
    title: qsTr("质量检测与分拣系统")
    
    // 连接C++信号
    Connections {
        target: sortingManager
        onMessageAdded: {
            messageModel.insert(0, {
                "message": message,
                "time": timestamp
            })
        }
        onDataImported: {
            // 数据导入完成后刷新记录列表
            recordModel.clear()
            var records = sortingManager.getDetectionRecords()
            for (var i = 0; i < records.length; i++) {
                recordModel.append(records[i])
            }
        }
        onRecordAdded: {
            recordModel.insert(0, record)
        }
    }
    
    RowLayout {
        anchors.fill: parent
        spacing: 5
        anchors.margins: 5
        
        // 左侧面板 - 消息记录
        ColumnLayout {
            Layout.fillHeight: true
            Layout.preferredWidth: 400
            spacing: 0

            Rectangle {
                Layout.fillWidth: true
                height: 30
                color: "#e0e0e0"
                border.color: "#c0c0c0"

                Text {
                    anchors.centerIn: parent
                    text: "消息记录"
                    font.bold: true
                    font.pixelSize: 14
                }
            }

            ListView {
                id: messageList
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 2
                clip: true
                spacing: 2
                verticalLayoutDirection: ListView.BottomToTop

                model: ListModel {
                    id: messageModel
                }
                
                delegate: Rectangle {
                    width: messageList.width
                    height: Math.max(30, messageText.contentHeight + 10)
                    color: "#f8f8f8"
                    border.color: "#e0e0e0"
                    radius: 3

                    Text {
                        id: messageText
                        anchors.left: parent.left
                        anchors.right: timeText.left
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        anchors.margins: 5
                        text: message
                        color: "#333333"
                        wrapMode: Text.WordWrap
                        font.pixelSize: 11
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    Text {
                        id: timeText
                        anchors.right: parent.right
                        anchors.top: parent.top
                        anchors.margins: 5
                        text: time
                        color: "#666666"
                        font.pixelSize: 10
                    }
                }
            }
            
            // 控制按钮区域
            Rectangle {
                Layout.fillWidth: true
                height: 120
                color: "#f0f0f0"
                border.color: "#e0e0e0"
                radius: 5

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 8

                    // 第一行按钮
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 10

                        Button {
                            text: qsTr("导入数据")
                            Layout.preferredWidth: 100
                            onClicked: {
                                var filePath = sortingManager.selectImportFile()
                                if (filePath !== "") {
                                    sortingManager.importDataFromFile(filePath)
                                }
                            }
                        }

                        Button {
                            text: qsTr("数据导出")
                            Layout.preferredWidth: 100
                            onClicked: {
                                var filePath = sortingManager.selectExportPath()
                                if (filePath !== "") {
                                    sortingManager.exportDataToCsv(filePath)
                                }
                            }
                        }
                    }

                    // 第二行按钮
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 10

                        Button {
                            text: qsTr("清空记录")
                            Layout.preferredWidth: 100
                            onClicked: {
                                sortingManager.clearRecords()
                                recordModel.clear()
                            }
                        }

                        Button {
                            text: qsTr("生成测试数据")
                            Layout.preferredWidth: 120
                            onClicked: {
                                sortingManager.generateTestData()
                            }
                        }
                    }

                    // 第三行按钮
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 10

                        Button {
                            text: qsTr("运行测试")
                            Layout.preferredWidth: 100
                            onClicked: {
                                sortingManager.runTests()
                            }
                        }
                    }
                }
            }
        }
        
        // 右侧面板 - 检测记录
        ColumnLayout {
            Layout.fillHeight: true
            Layout.fillWidth: true
            spacing: 0
            
            Rectangle {
                Layout.fillWidth: true
                height: 30
                color: "#e0e0e0"
                border.color: "#c0c0c0"

                Text {
                    anchors.centerIn: parent
                    text: "检测记录"
                    font.bold: true
                    font.pixelSize: 14
                }
            }
            
            // 表格标题行
            Rectangle {
                Layout.fillWidth: true
                height: 35
                color: "#f5f5f5"
                border.color: "#d0d0d0"
                
                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 5
                    spacing: 1
                    
                    Text {
                        Layout.preferredWidth: 120
                        text: "时间戳"
                        font.bold: true
                        font.pixelSize: 12
                        horizontalAlignment: Text.AlignHCenter
                    }
                    Rectangle { width: 1; Layout.fillHeight: true; color: "#d0d0d0" }
                    Text {
                        Layout.preferredWidth: 80
                        text: "条码"
                        font.bold: true
                        font.pixelSize: 12
                        horizontalAlignment: Text.AlignHCenter
                    }
                    Rectangle { width: 1; Layout.fillHeight: true; color: "#d0d0d0" }
                    Text {
                        Layout.preferredWidth: 60
                        text: "结果"
                        font.bold: true
                        font.pixelSize: 12
                        horizontalAlignment: Text.AlignHCenter
                    }
                    Rectangle { width: 1; Layout.fillHeight: true; color: "#d0d0d0" }
                    Text {
                        Layout.fillWidth: true
                        text: "缺陷信息"
                        font.bold: true
                        font.pixelSize: 12
                        horizontalAlignment: Text.AlignHCenter
                    }
                }
            }
            
            ListView {
                id: recordList
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 2
                clip: true
                spacing: 1
                
                model: ListModel {
                    id: recordModel
                }
                
                delegate: Rectangle {
                    width: recordList.width
                    height: 30
                    color: index % 2 === 0 ? "#ffffff" : "#f9f9f9"
                    border.color: "#e0e0e0"
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 5
                        spacing: 1
                        
                        Text {
                            Layout.preferredWidth: 120
                            text: timestamp
                            font.pixelSize: 10
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        Rectangle { width: 1; Layout.fillHeight: true; color: "#e0e0e0" }
                        Text {
                            Layout.preferredWidth: 80
                            text: barcode
                            font.pixelSize: 11
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        Rectangle { width: 1; Layout.fillHeight: true; color: "#e0e0e0" }
                        Text {
                            Layout.preferredWidth: 60
                            text: detectionResult
                            font.pixelSize: 11
                            color: detectionResult === "合格" ? "#008000" : "#ff0000"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            font.bold: true
                        }
                        Rectangle { width: 1; Layout.fillHeight: true; color: "#e0e0e0" }
                        Text {
                            Layout.fillWidth: true
                            text: defectInfo
                            font.pixelSize: 11
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            elide: Text.ElideRight
                        }
                    }
                }
            }

            // 手动添加记录区域
            Rectangle {
                Layout.fillWidth: true
                height: 60
                color: "#f8f8f8"
                border.color: "#e0e0e0"
                radius: 3

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 8
                    spacing: 5

                    Text {
                        text: "手动添加检测记录"
                        font.bold: true
                        font.pixelSize: 12
                    }

                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 5

                        TextField {
                            id: barcodeInput
                            Layout.preferredWidth: 80
                            placeholderText: "条码"
                            font.pixelSize: 11
                        }

                        ComboBox {
                            id: resultCombo
                            Layout.preferredWidth: 80
                            model: ["合格", "不合格"]
                            font.pixelSize: 11
                        }

                        TextField {
                            id: defectInput
                            Layout.fillWidth: true
                            placeholderText: "缺陷信息（合格时填'无'）"
                            font.pixelSize: 11
                        }

                        Button {
                            text: qsTr("添加")
                            Layout.preferredWidth: 60
                            onClicked: {
                                if (barcodeInput.text !== "") {
                                    var defect = defectInput.text === "" ? "无" : defectInput.text
                                    sortingManager.addDetectionRecord(
                                        barcodeInput.text,
                                        resultCombo.currentText,
                                        defect
                                    )
                                    barcodeInput.text = ""
                                    defectInput.text = ""
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
