{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}]}, "id": "sorting_autogen::@6890427a1f51a3e7e1df", "isGeneratorProvided": true, "name": "sorting_autogen", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}