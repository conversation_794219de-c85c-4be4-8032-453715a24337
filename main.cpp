#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include "sorting.h"

int main(int argc, char *argv[])
{
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);

    QGuiApplication app(argc, argv);

    // 创建排序管理器实例
    SortingManager sortingManager;

    QQmlApplicationEngine engine;

    // 将C++对象暴露给QML
    engine.rootContext()->setContextProperty("sortingManager", &sortingManager);

    const QUrl url(QStringLiteral("qrc:/main.qml"));
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);
    engine.load(url);

    return app.exec();
}
