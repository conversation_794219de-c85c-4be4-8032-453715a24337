# CMAKE generated file: DO NOT EDIT!
# Generated by "NMake Makefiles JOM" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

!IF "$(OS)" == "Windows_NT"
NULL=
!ELSE
NULL=nul
!ENDIF
SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\software\sorting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles\sorting.dir\all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles\sorting.dir\clean
clean: CMakeFiles\sorting_autogen.dir\clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/sorting.dir

# All Build rule for target.
CMakeFiles\sorting.dir\all: CMakeFiles\sorting_autogen.dir\all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=1,2,3,4,5,6 "Built target sorting"
.PHONY : CMakeFiles\sorting.dir\all

# Build rule for subdir invocation for target.
CMakeFiles\sorting.dir\rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles 0
.PHONY : CMakeFiles\sorting.dir\rule

# Convenience name for target.
sorting: CMakeFiles\sorting.dir\rule
.PHONY : sorting

# clean rule for target.
CMakeFiles\sorting.dir\clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\clean
.PHONY : CMakeFiles\sorting.dir\clean

#=============================================================================
# Target rules for target CMakeFiles/sorting_autogen.dir

# All Build rule for target.
CMakeFiles\sorting_autogen.dir\all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting_autogen.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting_autogen.dir\depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting_autogen.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting_autogen.dir\build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=7 "Built target sorting_autogen"
.PHONY : CMakeFiles\sorting_autogen.dir\all

# Build rule for subdir invocation for target.
CMakeFiles\sorting_autogen.dir\rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) CMakeFiles\sorting_autogen.dir\all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles 0
.PHONY : CMakeFiles\sorting_autogen.dir\rule

# Convenience name for target.
sorting_autogen: CMakeFiles\sorting_autogen.dir\rule
.PHONY : sorting_autogen

# clean rule for target.
CMakeFiles\sorting_autogen.dir\clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting_autogen.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting_autogen.dir\clean
.PHONY : CMakeFiles\sorting_autogen.dir\clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

