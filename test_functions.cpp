#include "sorting.h"
#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QTextStream>

/**
 * 测试函数集合
 * 演示如何使用SortingManager的各种功能
 */

void testBasicFunctionality()
{
    qDebug() << "=== 基础功能测试 ===";
    
    SortingManager manager;
    
    // 测试1: 添加检测记录
    qDebug() << "测试1: 添加检测记录";
    manager.addDetectionRecord("TEST001", "合格", "无");
    manager.addDetectionRecord("TEST002", "不合格", "尺寸超差");
    manager.addDetectionRecord("TEST003", "合格", "无");
    
    // 测试2: 获取记录数量
    qDebug() << "测试2: 获取记录数量";
    QVariantList records = manager.getDetectionRecords();
    qDebug() << "当前记录数量:" << records.size();
    
    // 测试3: 时间戳格式
    qDebug() << "测试3: 时间戳格式";
    QString timestamp = manager.getCurrentTimestamp();
    qDebug() << "当前时间戳:" << timestamp;
    
    qDebug() << "基础功能测试完成\n";
}

void testFileOperations()
{
    qDebug() << "=== 文件操作测试 ===";
    
    SortingManager manager;
    
    // 创建测试数据
    manager.addDetectionRecord("FILE001", "合格", "无");
    manager.addDetectionRecord("FILE002", "不合格", "表面划痕");
    manager.addDetectionRecord("FILE003", "合格", "无");
    
    // 测试导出功能
    qDebug() << "测试导出功能";
    QString exportPath = QDir::tempPath() + "/test_export.csv";
    bool exportResult = manager.exportDataToCsv(exportPath);
    qDebug() << "导出结果:" << (exportResult ? "成功" : "失败");
    
    if (exportResult) {
        // 验证导出文件内容
        QFile file(exportPath);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream in(&file);
            in.setCodec("UTF-8");
            qDebug() << "导出文件内容:";
            while (!in.atEnd()) {
                qDebug() << in.readLine();
            }
            file.close();
        }
        
        // 测试导入功能
        qDebug() << "\n测试导入功能";
        manager.clearRecords();
        bool importResult = manager.importDataFromFile(exportPath);
        qDebug() << "导入结果:" << (importResult ? "成功" : "失败");
        
        QVariantList importedRecords = manager.getDetectionRecords();
        qDebug() << "导入后记录数量:" << importedRecords.size();
        
        // 清理临时文件
        QFile::remove(exportPath);
    }
    
    qDebug() << "文件操作测试完成\n";
}

void testDataValidation()
{
    qDebug() << "=== 数据验证测试 ===";
    
    SortingManager manager;
    
    // 测试空数据导出
    qDebug() << "测试1: 空数据导出";
    QString emptyExportPath = QDir::tempPath() + "/empty_export.csv";
    bool emptyExportResult = manager.exportDataToCsv(emptyExportPath);
    qDebug() << "空数据导出结果:" << (emptyExportResult ? "成功" : "失败（预期）");
    
    // 测试无效文件路径导入
    qDebug() << "测试2: 无效文件路径导入";
    bool invalidImportResult = manager.importDataFromFile("invalid_path.csv");
    qDebug() << "无效路径导入结果:" << (invalidImportResult ? "成功" : "失败（预期）");
    
    // 测试空文件路径
    qDebug() << "测试3: 空文件路径";
    bool emptyPathResult = manager.importDataFromFile("");
    qDebug() << "空路径导入结果:" << (emptyPathResult ? "成功" : "失败（预期）");
    
    qDebug() << "数据验证测试完成\n";
}

void testPerformance()
{
    qDebug() << "=== 性能测试 ===";
    
    SortingManager manager;
    
    // 添加大量数据
    qDebug() << "添加1000条记录...";
    QTime timer;
    timer.start();
    
    for (int i = 0; i < 1000; ++i) {
        QString barcode = QString("PERF%1").arg(i, 4, 10, QChar('0'));
        QString result = (i % 3 == 0) ? "不合格" : "合格";
        QString defect = (result == "合格") ? "无" : QString("缺陷%1").arg(i % 5);
        manager.addDetectionRecord(barcode, result, defect);
    }
    
    int addTime = timer.elapsed();
    qDebug() << "添加1000条记录耗时:" << addTime << "毫秒";
    
    // 测试导出性能
    timer.restart();
    QString perfExportPath = QDir::tempPath() + "/perf_export.csv";
    manager.exportDataToCsv(perfExportPath);
    int exportTime = timer.elapsed();
    qDebug() << "导出1000条记录耗时:" << exportTime << "毫秒";
    
    // 测试导入性能
    manager.clearRecords();
    timer.restart();
    manager.importDataFromFile(perfExportPath);
    int importTime = timer.elapsed();
    qDebug() << "导入1000条记录耗时:" << importTime << "毫秒";
    
    // 清理
    QFile::remove(perfExportPath);
    
    qDebug() << "性能测试完成\n";
}

void runAllTests()
{
    qDebug() << "开始运行所有测试...\n";
    
    testBasicFunctionality();
    testFileOperations();
    testDataValidation();
    testPerformance();
    
    qDebug() << "所有测试完成!";
}

// 如果直接运行此文件作为测试程序
#ifdef TEST_MAIN
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    runAllTests();
    
    return 0;
}
#endif
