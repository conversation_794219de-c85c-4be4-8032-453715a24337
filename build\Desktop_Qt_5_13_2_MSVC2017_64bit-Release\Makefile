# CMAKE generated file: DO NOT EDIT!
# Generated by "NMake Makefiles JOM" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

!IF "$(OS)" == "Windows_NT"
NULL=
!ELSE
NULL=nul
!ENDIF
SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\software\sorting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	echo >nul && "C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache\fast: edit_cache
.PHONY : edit_cache\fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	echo >nul && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache\fast: rebuild_cache
.PHONY : rebuild_cache\fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) clean
.PHONY : clean

# The main clean target
clean\fast: clean
.PHONY : clean\fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall\fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) preinstall
.PHONY : preinstall\fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named sorting

# Build rule for target.
sorting: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) sorting
.PHONY : sorting

# fast build rule for target.
sorting\fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\build
.PHONY : sorting\fast

#=============================================================================
# Target rules for targets named sorting_autogen

# Build rule for target.
sorting_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 /nologo -$(MAKEFLAGS) sorting_autogen
.PHONY : sorting_autogen

# fast build rule for target.
sorting_autogen\fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting_autogen.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting_autogen.dir\build
.PHONY : sorting_autogen\fast

main.obj: main.cpp.obj
.PHONY : main.obj

# target to build an object file
main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\main.cpp.obj
.PHONY : main.cpp.obj

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\main.cpp.s
.PHONY : main.cpp.s

sorting.obj: sorting.cpp.obj
.PHONY : sorting.obj

# target to build an object file
sorting.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting.cpp.obj
.PHONY : sorting.cpp.obj

sorting.i: sorting.cpp.i
.PHONY : sorting.i

# target to preprocess a source file
sorting.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting.cpp.i
.PHONY : sorting.cpp.i

sorting.s: sorting.cpp.s
.PHONY : sorting.s

# target to generate assembly for a file
sorting.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting.cpp.s
.PHONY : sorting.cpp.s

sorting_autogen\EWIEGA46WW\qrc_qml.obj: sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj
.PHONY : sorting_autogen\EWIEGA46WW\qrc_qml.obj

# target to build an object file
sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj
.PHONY : sorting_autogen\EWIEGA46WW\qrc_qml.cpp.obj

sorting_autogen\EWIEGA46WW\qrc_qml.i: sorting_autogen\EWIEGA46WW\qrc_qml.cpp.i
.PHONY : sorting_autogen\EWIEGA46WW\qrc_qml.i

# target to preprocess a source file
sorting_autogen\EWIEGA46WW\qrc_qml.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.i
.PHONY : sorting_autogen\EWIEGA46WW\qrc_qml.cpp.i

sorting_autogen\EWIEGA46WW\qrc_qml.s: sorting_autogen\EWIEGA46WW\qrc_qml.cpp.s
.PHONY : sorting_autogen\EWIEGA46WW\qrc_qml.s

# target to generate assembly for a file
sorting_autogen\EWIEGA46WW\qrc_qml.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting_autogen\EWIEGA46WW\qrc_qml.cpp.s
.PHONY : sorting_autogen\EWIEGA46WW\qrc_qml.cpp.s

sorting_autogen\mocs_compilation.obj: sorting_autogen\mocs_compilation.cpp.obj
.PHONY : sorting_autogen\mocs_compilation.obj

# target to build an object file
sorting_autogen\mocs_compilation.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.obj
.PHONY : sorting_autogen\mocs_compilation.cpp.obj

sorting_autogen\mocs_compilation.i: sorting_autogen\mocs_compilation.cpp.i
.PHONY : sorting_autogen\mocs_compilation.i

# target to preprocess a source file
sorting_autogen\mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.i
.PHONY : sorting_autogen\mocs_compilation.cpp.i

sorting_autogen\mocs_compilation.s: sorting_autogen\mocs_compilation.cpp.s
.PHONY : sorting_autogen\mocs_compilation.s

# target to generate assembly for a file
sorting_autogen\mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sorting.dir\build.make /nologo -$(MAKEFLAGS) CMakeFiles\sorting.dir\sorting_autogen\mocs_compilation.cpp.s
.PHONY : sorting_autogen\mocs_compilation.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... sorting_autogen
	@echo ... sorting
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... sorting.obj
	@echo ... sorting.i
	@echo ... sorting.s
	@echo ... sorting_autogen/EWIEGA46WW/qrc_qml.obj
	@echo ... sorting_autogen/EWIEGA46WW/qrc_qml.i
	@echo ... sorting_autogen/EWIEGA46WW/qrc_qml.s
	@echo ... sorting_autogen/mocs_compilation.obj
	@echo ... sorting_autogen/mocs_compilation.i
	@echo ... sorting_autogen/mocs_compilation.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

