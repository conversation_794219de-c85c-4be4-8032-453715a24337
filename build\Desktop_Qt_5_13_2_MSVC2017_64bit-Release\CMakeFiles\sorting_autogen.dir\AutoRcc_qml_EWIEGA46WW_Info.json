{"BUILD_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen", "CMAKE_BINARY_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "CMAKE_CURRENT_BINARY_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "CMAKE_CURRENT_SOURCE_DIR": "E:/software/sorting", "CMAKE_SOURCE_DIR": "E:/software/sorting", "CROSS_CONFIG": false, "GENERATOR": "NMake Makefiles JOM", "INCLUDE_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen/include", "INPUTS": ["E:/software/sorting/main.qml"], "LOCK_FILE": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "qml"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_qml.cpp", "RCC_EXECUTABLE": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used.txt", "SOURCE": "E:/software/sorting/qml.qrc", "USE_BETTER_GRAPH": false, "VERBOSITY": 0}