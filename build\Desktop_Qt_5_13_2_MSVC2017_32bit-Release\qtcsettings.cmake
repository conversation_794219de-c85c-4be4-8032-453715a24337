# This file is managed by Qt Creator, do not edit!

set("QT_QMAKE_EXECUTABLE" "D:/Qt/Qt5.13.2/5.13.2/msvc2017/bin/qmake.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PROJECT_INCLUDE_BEFORE" "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)
set("CMAKE_C_COMPILER" "C:/Program Files (x86)/Microsoft Visual Studio/2017/Community/VC/Tools/MSVC/14.16.27023/bin/HostX64/x86/cl.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PREFIX_PATH" "D:/Qt/Qt5.13.2/5.13.2/msvc2017" CACHE "PATH" "" FORCE)
set("CMAKE_CXX_COMPILER" "C:/Program Files (x86)/Microsoft Visual Studio/2017/Community/VC/Tools/MSVC/14.16.27023/bin/HostX64/x86/cl.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_BUILD_TYPE" "Release" CACHE "STRING" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "" CACHE "STRING" "" FORCE)
set("CMAKE_GENERATOR" "Ninja" CACHE "STRING" "" FORCE)