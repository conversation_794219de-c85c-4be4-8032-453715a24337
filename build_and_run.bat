@echo off
echo 质量检测与分拣系统 - 编译和运行脚本
echo =====================================

echo.
echo 1. 配置CMake...
cmake -B build-Debug -S .
if %errorlevel% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)

echo.
echo 2. 编译项目...
cmake --build build-Debug
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 3. 编译成功！启动程序...
echo.
start "" "build-Debug\sorting.exe"

echo 程序已启动，请查看新打开的窗口。
echo.
echo 使用说明：
echo - 点击"生成测试数据"可以快速创建示例数据
echo - 点击"导入数据"可以导入test_data.csv文件
echo - 点击"运行测试"可以验证系统功能
echo.
pause
