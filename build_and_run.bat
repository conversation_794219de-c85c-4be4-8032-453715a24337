@echo off
echo Quality Detection and Sorting System - Build and Run Script
echo ===========================================================

echo.
echo 1. Configuring CMake...
cmake -B build-Debug -S .
if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo 2. Building project...
cmake --build build-Debug
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo 3. Build successful! Starting application...
echo.
start "" "build-Debug\Debug\sorting.exe"

echo Application started, please check the new window.
echo.
echo Usage Instructions:
echo - Click "Test Data" to quickly create sample data
echo - Click "Import Data" to load test_data.csv file
echo - Click "Run Tests" to verify system functionality
echo - Use the manual entry form to add individual records
echo - Export data to save records as CSV files
echo.
echo For detailed instructions, see QUICK_START.md
echo.
pause
