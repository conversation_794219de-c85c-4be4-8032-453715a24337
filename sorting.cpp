#include "sorting.h"
#include <QFileDialog>
#include <QStandardPaths>
#include <QTextStream>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDir>
#include <QCoreApplication>

SortingManager::SortingManager(QObject *parent)
    : QObject(parent)
{
    addMessage("排序管理器初始化完成");
}

QString SortingManager::selectImportFile()
{
    QString fileName = QFileDialog::getOpenFileName(
        nullptr,
        "选择导入文件",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "CSV文件 (*.csv);;JSON文件 (*.json);;所有文件 (*.*)"
    );

    if (!fileName.isEmpty()) {
        addMessage(QString("选择了导入文件: %1").arg(fileName));
    }

    return fileName;
}

bool SortingManager::importDataFromFile(const QString& filePath)
{
    if (filePath.isEmpty()) {
        addMessage("导入失败: 文件路径为空");
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        addMessage(QString("导入失败: 无法打开文件 %1").arg(filePath));
        return false;
    }

    QTextStream in(&file);
    in.setCodec("UTF-8");

    int importedCount = 0;
    bool isFirstLine = true;

    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty()) continue;

        // 跳过CSV标题行
        if (isFirstLine && line.contains("时间戳")) {
            isFirstLine = false;
            continue;
        }
        isFirstLine = false;

        QStringList fields = line.split(',');
        if (fields.size() >= 4) {
            DetectionRecord record;
            record.timestamp = QDateTime::fromString(fields[0].trimmed(), "yyyy-MM-dd hh:mm:ss");
            record.barcode = fields[1].trimmed();
            record.detectionResult = fields[2].trimmed();
            record.defectInfo = fields[3].trimmed();

            m_records.append(record);
            importedCount++;

            emit recordAdded(recordToVariantMap(record));
        }
    }

    file.close();

    addMessage(QString("成功导入 %1 条记录").arg(importedCount));
    emit dataImported(importedCount);

    return importedCount > 0;
}

QString SortingManager::selectExportPath()
{
    QString fileName = QFileDialog::getSaveFileName(
        nullptr,
        "选择导出路径",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/detection_records.csv",
        "CSV文件 (*.csv)"
    );

    if (!fileName.isEmpty()) {
        addMessage(QString("选择了导出路径: %1").arg(fileName));
    }

    return fileName;
}

bool SortingManager::exportDataToCsv(const QString& filePath)
{
    if (filePath.isEmpty()) {
        addMessage("导出失败: 文件路径为空");
        return false;
    }

    if (m_records.isEmpty()) {
        addMessage("导出失败: 没有数据可导出");
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        addMessage(QString("导出失败: 无法创建文件 %1").arg(filePath));
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    // 写入CSV标题行
    out << "时间戳,条码信息,检测结果,缺陷信息\n";

    // 写入数据行
    for (const auto& record : m_records) {
        out << record.timestamp.toString("yyyy-MM-dd hh:mm:ss") << ","
            << record.barcode << ","
            << record.detectionResult << ","
            << record.defectInfo << "\n";
    }

    file.close();

    addMessage(QString("成功导出 %1 条记录到文件: %2").arg(m_records.size()).arg(filePath));
    emit dataExported(filePath);

    return true;
}

void SortingManager::addDetectionRecord(const QString& barcode, const QString& result, const QString& defect)
{
    DetectionRecord record(barcode, result, defect);
    m_records.append(record);

    addMessage(QString("添加检测记录: 条码=%1, 结果=%2, 缺陷=%3")
               .arg(barcode).arg(result).arg(defect));

    emit recordAdded(recordToVariantMap(record));
}

QVariantList SortingManager::getDetectionRecords() const
{
    QVariantList list;
    for (const auto& record : m_records) {
        list.append(recordToVariantMap(record));
    }
    return list;
}

void SortingManager::clearRecords()
{
    int count = m_records.size();
    m_records.clear();
    addMessage(QString("清空了 %1 条记录").arg(count));
}

QString SortingManager::getCurrentTimestamp() const
{
    return QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
}

void SortingManager::generateTestData()
{
    addMessage("开始生成测试数据...");

    // 清空现有数据
    m_records.clear();

    // 生成测试数据
    QStringList barcodes = {"BC001", "BC002", "BC003", "BC004", "BC005", "BC006", "BC007", "BC008", "BC009", "BC010"};
    QStringList results = {"合格", "不合格", "合格", "不合格", "合格", "合格", "不合格", "合格", "合格", "不合格"};
    QStringList defects = {"无", "尺寸超差", "无", "表面划痕", "无", "无", "颜色异常", "无", "无", "形状不规则"};

    for (int i = 0; i < barcodes.size(); ++i) {
        DetectionRecord record;
        record.timestamp = QDateTime::currentDateTime().addSecs(-3600 + i * 360); // 每6分钟一条记录
        record.barcode = barcodes[i];
        record.detectionResult = results[i];
        record.defectInfo = defects[i];

        m_records.append(record);
        emit recordAdded(recordToVariantMap(record));
    }

    addMessage(QString("成功生成 %1 条测试数据").arg(barcodes.size()));
}

bool SortingManager::runTests()
{
    addMessage("开始运行测试...");

    bool allTestsPassed = true;
    int testCount = 0;
    int passedCount = 0;

    // 测试1: 添加记录功能
    testCount++;
    int initialCount = m_records.size();
    addDetectionRecord("TEST001", "合格", "无");
    if (m_records.size() == initialCount + 1) {
        addMessage("✓ 测试1通过: 添加记录功能正常");
        passedCount++;
    } else {
        addMessage("✗ 测试1失败: 添加记录功能异常");
        allTestsPassed = false;
    }

    // 测试2: 时间戳格式测试
    testCount++;
    QString timestamp = getCurrentTimestamp();
    QDateTime dt = QDateTime::fromString(timestamp, "yyyy-MM-dd hh:mm:ss");
    if (dt.isValid()) {
        addMessage("✓ 测试2通过: 时间戳格式正确");
        passedCount++;
    } else {
        addMessage("✗ 测试2失败: 时间戳格式错误");
        allTestsPassed = false;
    }

    // 测试3: 导出功能测试（创建临时文件）
    testCount++;
    QString tempPath = QDir::tempPath() + "/test_export.csv";
    if (exportDataToCsv(tempPath)) {
        QFile tempFile(tempPath);
        if (tempFile.exists()) {
            addMessage("✓ 测试3通过: 导出功能正常");
            passedCount++;
            tempFile.remove(); // 清理临时文件
        } else {
            addMessage("✗ 测试3失败: 导出文件未创建");
            allTestsPassed = false;
        }
    } else {
        addMessage("✗ 测试3失败: 导出功能异常");
        allTestsPassed = false;
    }

    // 测试4: 数据清空功能
    testCount++;
    int beforeClear = m_records.size();
    clearRecords();
    if (m_records.isEmpty() && beforeClear > 0) {
        addMessage("✓ 测试4通过: 清空功能正常");
        passedCount++;
    } else {
        addMessage("✗ 测试4失败: 清空功能异常");
        allTestsPassed = false;
    }

    addMessage(QString("测试完成: %1/%2 通过").arg(passedCount).arg(testCount));

    if (allTestsPassed) {
        addMessage("🎉 所有测试通过!");
    } else {
        addMessage("❌ 部分测试失败，请检查功能实现");
    }

    return allTestsPassed;
}

void SortingManager::addMessage(const QString& message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    emit messageAdded(message, timestamp);
}

QVariantMap SortingManager::recordToVariantMap(const DetectionRecord& record) const
{
    QVariantMap map;
    map["timestamp"] = record.timestamp.toString("yyyy-MM-dd hh:mm:ss");
    map["barcode"] = record.barcode;
    map["detectionResult"] = record.detectionResult;
    map["defectInfo"] = record.defectInfo;
    return map;
}

#include "sorting.moc"
