#include "sorting.h"
#include <QtWidgets/QFileDialog>
#include <QStandardPaths>
#include <QTextStream>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDir>
#include <QCoreApplication>
#include <QFile>
#include <QDateTime>
#include <QTime>

SortingManager::SortingManager(QObject *parent)
    : QObject(parent)
{
    addMessage("Sorting Manager initialized successfully");
}

QString SortingManager::selectImportFile()
{
    QString fileName = QFileDialog::getOpenFileName(
        nullptr,
        "Select Import File",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "CSV Files (*.csv);;JSON Files (*.json);;All Files (*.*)"
    );

    if (!fileName.isEmpty()) {
        addMessage(QString("Selected import file: %1").arg(fileName));
    }

    return fileName;
}

bool SortingManager::importDataFromFile(const QString& filePath)
{
    if (filePath.isEmpty()) {
        addMessage("Import failed: File path is empty");
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        addMessage(QString("Import failed: Cannot open file %1").arg(filePath));
        return false;
    }

    QTextStream in(&file);
    in.setCodec("UTF-8");

    int importedCount = 0;
    bool isFirstLine = true;

    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty()) continue;

        // Skip CSV header line
        if (isFirstLine && (line.contains("Timestamp") || line.contains("时间戳"))) {
            isFirstLine = false;
            continue;
        }
        isFirstLine = false;

        QStringList fields = line.split(',');
        if (fields.size() >= 4) {
            DetectionRecord record;
            record.timestamp = QDateTime::fromString(fields[0].trimmed(), "yyyy-MM-dd hh:mm:ss");
            record.barcode = fields[1].trimmed();
            record.detectionResult = fields[2].trimmed();
            record.defectInfo = fields[3].trimmed();

            m_records.append(record);
            importedCount++;

            emit recordAdded(recordToVariantMap(record));
        }
    }

    file.close();

    addMessage(QString("Successfully imported %1 records").arg(importedCount));
    emit dataImported(importedCount);

    return importedCount > 0;
}

QString SortingManager::selectExportPath()
{
    QString fileName = QFileDialog::getSaveFileName(
        nullptr,
        "Select Export Path",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/detection_records.csv",
        "CSV Files (*.csv)"
    );

    if (!fileName.isEmpty()) {
        addMessage(QString("Selected export path: %1").arg(fileName));
    }

    return fileName;
}

bool SortingManager::exportDataToCsv(const QString& filePath)
{
    if (filePath.isEmpty()) {
        addMessage("Export failed: File path is empty");
        return false;
    }

    if (m_records.isEmpty()) {
        addMessage("Export failed: No data to export");
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        addMessage(QString("Export failed: Cannot create file %1").arg(filePath));
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    // Write CSV header
    out << "Timestamp,Barcode,Result,DefectInfo\n";

    // Write data rows
    for (const auto& record : m_records) {
        out << record.timestamp.toString("yyyy-MM-dd hh:mm:ss") << ","
            << record.barcode << ","
            << record.detectionResult << ","
            << record.defectInfo << "\n";
    }

    file.close();

    addMessage(QString("Successfully exported %1 records to file: %2").arg(m_records.size()).arg(filePath));
    emit dataExported(filePath);

    return true;
}

void SortingManager::addDetectionRecord(const QString& barcode, const QString& result, const QString& defect)
{
    DetectionRecord record(barcode, result, defect);
    m_records.append(record);

    addMessage(QString("Added detection record: Barcode=%1, Result=%2, Defect=%3")
               .arg(barcode).arg(result).arg(defect));

    emit recordAdded(recordToVariantMap(record));
}

QVariantList SortingManager::getDetectionRecords() const
{
    QVariantList list;
    for (const auto& record : m_records) {
        list.append(recordToVariantMap(record));
    }
    return list;
}

void SortingManager::clearRecords()
{
    int count = m_records.size();
    m_records.clear();
    addMessage(QString("Cleared %1 records").arg(count));
}

QString SortingManager::getCurrentTimestamp() const
{
    return QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
}

void SortingManager::generateTestData()
{
    addMessage("Starting to generate test data...");

    // Clear existing data
    m_records.clear();

    // Generate test data
    QStringList barcodes = {"BC001", "BC002", "BC003", "BC004", "BC005", "BC006", "BC007", "BC008", "BC009", "BC010"};
    QStringList results = {"Pass", "Fail", "Pass", "Fail", "Pass", "Pass", "Fail", "Pass", "Pass", "Fail"};
    QStringList defects = {"None", "Size deviation", "None", "Surface scratch", "None", "None", "Color abnormal", "None", "None", "Shape irregular"};

    for (int i = 0; i < barcodes.size(); ++i) {
        DetectionRecord record;
        record.timestamp = QDateTime::currentDateTime().addSecs(-3600 + i * 360); // One record every 6 minutes
        record.barcode = barcodes[i];
        record.detectionResult = results[i];
        record.defectInfo = defects[i];

        m_records.append(record);
        emit recordAdded(recordToVariantMap(record));
    }

    addMessage(QString("Successfully generated %1 test records").arg(barcodes.size()));
}

bool SortingManager::runTests()
{
    addMessage("Starting to run tests...");

    bool allTestsPassed = true;
    int testCount = 0;
    int passedCount = 0;

    // Test 1: Add record functionality
    testCount++;
    int initialCount = m_records.size();
    addDetectionRecord("TEST001", "Pass", "None");
    if (m_records.size() == initialCount + 1) {
        addMessage("[PASS] Test 1: Add record function works");
        passedCount++;
    } else {
        addMessage("[FAIL] Test 1: Add record function error");
        allTestsPassed = false;
    }

    // Test 2: Timestamp format test
    testCount++;
    QString timestamp = getCurrentTimestamp();
    QDateTime dt = QDateTime::fromString(timestamp, "yyyy-MM-dd hh:mm:ss");
    if (dt.isValid()) {
        addMessage("[PASS] Test 2: Timestamp format correct");
        passedCount++;
    } else {
        addMessage("[FAIL] Test 2: Timestamp format error");
        allTestsPassed = false;
    }

    // Test 3: Export function test (create temporary file)
    testCount++;
    QString tempPath = QDir::tempPath() + "/test_export.csv";
    if (exportDataToCsv(tempPath)) {
        QFile tempFile(tempPath);
        if (tempFile.exists()) {
            addMessage("[PASS] Test 3: Export function works");
            passedCount++;
            tempFile.remove(); // Clean up temporary file
        } else {
            addMessage("[FAIL] Test 3: Export file not created");
            allTestsPassed = false;
        }
    } else {
        addMessage("[FAIL] Test 3: Export function error");
        allTestsPassed = false;
    }

    // Test 4: Clear records function
    testCount++;
    int beforeClear = m_records.size();
    clearRecords();
    if (m_records.isEmpty() && beforeClear > 0) {
        addMessage("[PASS] Test 4: Clear function works");
        passedCount++;
    } else {
        addMessage("[FAIL] Test 4: Clear function error");
        allTestsPassed = false;
    }

    addMessage(QString("Tests completed: %1/%2 passed").arg(passedCount).arg(testCount));

    if (allTestsPassed) {
        addMessage("*** All tests passed! ***");
    } else {
        addMessage("*** Some tests failed, please check implementation ***");
    }

    return allTestsPassed;
}

void SortingManager::addMessage(const QString& message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    emit messageAdded(message, timestamp);
}

QVariantMap SortingManager::recordToVariantMap(const DetectionRecord& record) const
{
    QVariantMap map;
    map["timestamp"] = record.timestamp.toString("yyyy-MM-dd hh:mm:ss");
    map["barcode"] = record.barcode;
    map["detectionResult"] = record.detectionResult;
    map["defectInfo"] = record.defectInfo;
    return map;
}
