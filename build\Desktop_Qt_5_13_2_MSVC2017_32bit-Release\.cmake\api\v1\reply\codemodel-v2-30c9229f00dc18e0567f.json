{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.5"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "sorting", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "sorting::@6890427a1f51a3e7e1df", "jsonFile": "target-sorting-Release-d1ba26a4b074bc901e47.json", "name": "sorting", "projectIndex": 0}, {"directoryIndex": 0, "id": "sorting_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-sorting_autogen-Release-ea4d176032cf5faafc46.json", "name": "sorting_autogen", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release", "source": "E:/software/sorting"}, "version": {"major": 2, "minor": 7}}