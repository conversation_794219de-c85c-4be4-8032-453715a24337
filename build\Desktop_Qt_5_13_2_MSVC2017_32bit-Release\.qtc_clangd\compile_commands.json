[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-W3", "-GR", "-EHsc", "-DNDEBUG", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.16", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_32bit-Release", "-IE:\\software\\sorting", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\\sorting_autogen\\include", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtCore", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtQuick", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtQml", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtNetwork", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtGui", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtANGLE", "/clang:-isystem", "/clang:C:\\Qt\\qtcreator-15.0.0\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "E:\\software\\sorting\\main.cpp"], "directory": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release/.qtc_clangd", "file": "E:/software/sorting/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-W3", "-GR", "-EHsc", "-DNDEBUG", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.16", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_32bit-Release", "-IE:\\software\\sorting", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\\sorting_autogen\\include", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtCore", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtQuick", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtQml", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtNetwork", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtGui", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtANGLE", "/clang:-isystem", "/clang:C:\\Qt\\qtcreator-15.0.0\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "E:\\software\\sorting\\sorting.cpp"], "directory": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release/.qtc_clangd", "file": "E:/software/sorting/sorting.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-W3", "-GR", "-EHsc", "-DNDEBUG", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.16", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_32bit-Release", "-IE:\\software\\sorting", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_32bit-Release\\sorting_autogen\\include", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtCore", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtQuick", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtQml", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtNetwork", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtGui", "/clang:-isystem", "/clang:D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017\\include\\QtANGLE", "/clang:-isystem", "/clang:C:\\Qt\\qtcreator-15.0.0\\bin\\clang\\lib\\clang\\19\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Community\\VC\\Tools\\MSVC\\14.16.27023\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "E:\\software\\sorting\\sorting.h"], "directory": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Release/.qtc_clangd", "file": "E:/software/sorting/sorting.h"}]