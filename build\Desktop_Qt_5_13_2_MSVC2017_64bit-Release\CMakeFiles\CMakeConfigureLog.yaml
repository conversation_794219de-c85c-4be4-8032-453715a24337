
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/LLVM/bin/clang++.exe 
      Build flags: 
      Id flags: -c;--target=i686-w64-mingw32 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is Clang, found in:
        E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:67 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/CMakeScratch/TryCompile-651ho9"
      binary: "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/CMakeScratch/TryCompile-651ho9"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "D:/LLVM/bin/clang-scan-deps.exe"
      CMAKE_CXX_COMPILER_TARGET: "i686-w64-mingw32"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_C_COMPILER_TARGET: "i686-w64-mingw32"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_SYSROOT: "D:/Qt/Qt5.13.2/Tools/mingw730_32"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/CMakeScratch/TryCompile-651ho9'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 jom -f Makefile /nologo cmTC_da91b\\fast
        jom: parallel job execution disabled for Makefile
        	C:\\Qt\\qtcreator-15.0.0\\bin\\jom\\jom.exe  -f CMakeFiles\\cmTC_da91b.dir\\build.make /nologo -L CMakeFiles\\cmTC_da91b.dir\\build
        Building CXX object CMakeFiles/cmTC_da91b.dir/CMakeCXXCompilerABI.cpp.obj
        	D:\\LLVM\\bin\\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32   -fansi-escape-codes -fcolor-diagnostics   -v -MD -MT CMakeFiles\\cmTC_da91b.dir\\CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_da91b.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles\\cmTC_da91b.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.29\\Modules\\CMakeCXXCompilerABI.cpp"
        clang version 12.0.0
        Target: i686-w64-windows-gnu
        Thread model: posix
        InstalledDir: D:\\LLVM\\bin
         (in-process)
         "D:\\\\LLVM\\\\bin\\\\clang++.exe" -cc1 -triple i686-w64-windows-gnu -emit-obj -mrelax-all --mrelax-relocations -disable-free -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model static -mframe-pointer=all -fmath-errno -fno-rounding-math -mconstructor-aliases -mms-bitfields -target-cpu pentium4 -tune-cpu generic -debugger-tuning=gdb -v -resource-dir "D:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0" -dependency-file "CMakeFiles\\\\cmTC_da91b.dir\\\\CMakeCXXCompilerABI.cpp.obj.d" -MT "CMakeFiles\\\\cmTC_da91b.dir\\\\CMakeCXXCompilerABI.cpp.obj" -sys-header-deps -isysroot D:/Qt/Qt5.13.2/Tools/mingw730_32 -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\backward" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\7.3.0" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\7.3.0\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\7.3.0\\\\backward" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include\\\\c++\\\\7.3.0" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include\\\\c++\\\\7.3.0\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include\\\\c++\\\\7.3.0\\\\backward" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\include\\\\c++" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\include\\\\c++\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\include\\\\c++\\\\backward" -internal-isystem "D:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0\\\\include" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32/sys-root/mingw/include" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include" -fdeprecated-macro -fdebug-compilation-dir "E:\\\\software\\\\sorting\\\\build\\\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\\\\CMakeFiles\\\\CMakeScratch\\\\TryCompile-651ho9" -ferror-limit 19 -fno-use-cxa-atexit -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -fdwarf-exceptions -fcolor-diagnostics -fansi-escape-codes -faddrsig -o "CMakeFiles\\\\cmTC_da91b.dir\\\\CMakeCXXCompilerABI.cpp.obj" -x c++ "C:\\\\Program Files\\\\CMake\\\\share\\\\cmake-3.29\\\\Modules\\\\CMakeCXXCompilerABI.cpp"
        clang -cc1 version 12.0.0 based upon LLVM 12.0.0-6923b0a7 default target x86_64-pc-windows-msvc
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\i686-w64-mingw32"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\backward"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\7.3.0"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\7.3.0\\i686-w64-mingw32"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\7.3.0\\backward"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\include\\c++\\7.3.0"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\include\\c++\\7.3.0\\i686-w64-mingw32"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\include\\c++\\7.3.0\\backward"
        ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32/sys-root/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/Qt/Qt5.13.2/Tools/mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++
         D:/Qt/Qt5.13.2/Tools/mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\i686-w64-mingw32
         D:/Qt/Qt5.13.2/Tools/mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\backward
         D:\\LLVM\\lib\\clang\\12.0.0\\include
         D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include
         D:/Qt/Qt5.13.2/Tools/mingw730_32\\include
        End of search list.
        Linking CXX executable cmTC_da91b.exe
        	"C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_da91b.dir/objects.a
        	D:\\LLVM\\bin\\llvm-ar.exe qc CMakeFiles\\cmTC_da91b.dir/objects.a @CMakeFiles\\cmTC_da91b.dir\\objects1.rsp
        	D:\\LLVM\\bin\\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_da91b.dir/objects.a -Wl,--no-whole-archive -o cmTC_da91b.exe -Wl,--out-implib,libcmTC_da91b.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        clang version 12.0.0
        Target: i686-w64-windows-gnu
        Thread model: posix
        InstalledDir: D:\\LLVM\\bin
         "D:\\\\Qt\\\\Qt5.13.2\\\\Tools\\\\mingw730_32\\\\bin\\\\ld.exe" --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 -m i386pe -Bdynamic -o cmTC_da91b.exe "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib\\\\crt2.o" "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtbegin.o" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32/sys-root/mingw/lib" "-LD:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0\\\\lib\\\\windows" -v --whole-archive "CMakeFiles\\\\cmTC_da91b.dir/objects.a" --no-whole-archive --out-implib libcmTC_da91b.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtend.o"
        GNU ld (GNU Binutils) 2.30
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:137 (message)"
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++]
          add: [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32]
          add: [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward]
          add: [D:/LLVM/lib/clang/12.0.0/include]
          add: [D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include]
          add: [D:/Qt/Qt5.13.2/Tools/mingw730_32/include]
        end of search list found
        collapse include dir [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++]
        collapse include dir [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32]
        collapse include dir [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward]
        collapse include dir [D:/LLVM/lib/clang/12.0.0/include] ==> [D:/LLVM/lib/clang/12.0.0/include]
        collapse include dir [D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include]
        collapse include dir [D:/Qt/Qt5.13.2/Tools/mingw730_32/include] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/include]
        implicit include dirs: [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++;D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32;D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward;D:/LLVM/lib/clang/12.0.0/include;D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include;D:/Qt/Qt5.13.2/Tools/mingw730_32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:173 (message)"
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/CMakeScratch/TryCompile-651ho9']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 jom -f Makefile /nologo cmTC_da91b\\fast]
        ignore line: [jom: parallel job execution disabled for Makefile]
        ignore line: [	C:\\Qt\\qtcreator-15.0.0\\bin\\jom\\jom.exe  -f CMakeFiles\\cmTC_da91b.dir\\build.make /nologo -L CMakeFiles\\cmTC_da91b.dir\\build]
        ignore line: [Building CXX object CMakeFiles/cmTC_da91b.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [	D:\\LLVM\\bin\\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32   -fansi-escape-codes -fcolor-diagnostics   -v -MD -MT CMakeFiles\\cmTC_da91b.dir\\CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_da91b.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles\\cmTC_da91b.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.29\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [clang version 12.0.0]
        ignore line: [Target: i686-w64-windows-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: D:\\LLVM\\bin]
        ignore line: [ (in-process)]
        ignore line: [ "D:\\\\LLVM\\\\bin\\\\clang++.exe" -cc1 -triple i686-w64-windows-gnu -emit-obj -mrelax-all --mrelax-relocations -disable-free -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model static -mframe-pointer=all -fmath-errno -fno-rounding-math -mconstructor-aliases -mms-bitfields -target-cpu pentium4 -tune-cpu generic -debugger-tuning=gdb -v -resource-dir "D:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0" -dependency-file "CMakeFiles\\\\cmTC_da91b.dir\\\\CMakeCXXCompilerABI.cpp.obj.d" -MT "CMakeFiles\\\\cmTC_da91b.dir\\\\CMakeCXXCompilerABI.cpp.obj" -sys-header-deps -isysroot D:/Qt/Qt5.13.2/Tools/mingw730_32 -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\backward" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\7.3.0" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\7.3.0\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include\\\\c++\\\\7.3.0\\\\backward" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include\\\\c++\\\\7.3.0" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include\\\\c++\\\\7.3.0\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include\\\\c++\\\\7.3.0\\\\backward" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\include\\\\c++" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\include\\\\c++\\\\i686-w64-mingw32" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\include\\\\c++\\\\backward" -internal-isystem "D:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0\\\\include" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32/sys-root/mingw/include" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\include" -internal-isystem "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\include" -fdeprecated-macro -fdebug-compilation-dir "E:\\\\software\\\\sorting\\\\build\\\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\\\\CMakeFiles\\\\CMakeScratch\\\\TryCompile-651ho9" -ferror-limit 19 -fno-use-cxa-atexit -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -fdwarf-exceptions -fcolor-diagnostics -fansi-escape-codes -faddrsig -o "CMakeFiles\\\\cmTC_da91b.dir\\\\CMakeCXXCompilerABI.cpp.obj" -x c++ "C:\\\\Program Files\\\\CMake\\\\share\\\\cmake-3.29\\\\Modules\\\\CMakeCXXCompilerABI.cpp"]
        ignore line: [clang -cc1 version 12.0.0 based upon LLVM 12.0.0-6923b0a7 default target x86_64-pc-windows-msvc]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\i686-w64-mingw32"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\backward"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\7.3.0"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\7.3.0\\i686-w64-mingw32"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include\\c++\\7.3.0\\backward"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\include\\c++\\7.3.0"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\include\\c++\\7.3.0\\i686-w64-mingw32"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\include\\c++\\7.3.0\\backward"]
        ignore line: [ignoring nonexistent directory "D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32/sys-root/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/Qt/Qt5.13.2/Tools/mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++]
        ignore line: [ D:/Qt/Qt5.13.2/Tools/mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\i686-w64-mingw32]
        ignore line: [ D:/Qt/Qt5.13.2/Tools/mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\backward]
        ignore line: [ D:\\LLVM\\lib\\clang\\12.0.0\\include]
        ignore line: [ D:/Qt/Qt5.13.2/Tools/mingw730_32\\i686-w64-mingw32\\include]
        ignore line: [ D:/Qt/Qt5.13.2/Tools/mingw730_32\\include]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_da91b.exe]
        ignore line: [	"C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_da91b.dir/objects.a]
        ignore line: [	D:\\LLVM\\bin\\llvm-ar.exe qc CMakeFiles\\cmTC_da91b.dir/objects.a @CMakeFiles\\cmTC_da91b.dir\\objects1.rsp]
        ignore line: [	D:\\LLVM\\bin\\clang++.exe --target=i686-w64-mingw32 --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_da91b.dir/objects.a -Wl --no-whole-archive -o cmTC_da91b.exe -Wl --out-implib libcmTC_da91b.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [clang version 12.0.0]
        ignore line: [Target: i686-w64-windows-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: D:\\LLVM\\bin]
        link line: [ "D:\\\\Qt\\\\Qt5.13.2\\\\Tools\\\\mingw730_32\\\\bin\\\\ld.exe" --sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32 -m i386pe -Bdynamic -o cmTC_da91b.exe "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib\\\\crt2.o" "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtbegin.o" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib" "-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32/sys-root/mingw/lib" "-LD:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0\\\\lib\\\\windows" -v --whole-archive "CMakeFiles\\\\cmTC_da91b.dir/objects.a" --no-whole-archive --out-implib libcmTC_da91b.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lkernel32 "D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtend.o"]
          arg [D:\\\\Qt\\\\Qt5.13.2\\\\Tools\\\\mingw730_32\\\\bin\\\\ld.exe] ==> ignore
          arg [--sysroot=D:/Qt/Qt5.13.2/Tools/mingw730_32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pe] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_da91b.exe] ==> ignore
          arg [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib\\\\crt2.o] ==> obj [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib\\\\crt2.o]
          arg [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtbegin.o] ==> obj [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtbegin.o]
          arg [-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0] ==> dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0]
          arg [-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib] ==> dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib]
          arg [-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib] ==> dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib]
          arg [-LD:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32/sys-root/mingw/lib] ==> dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32/sys-root/mingw/lib]
          arg [-LD:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0\\\\lib\\\\windows] ==> dir [D:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0\\\\lib\\\\windows]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\\\cmTC_da91b.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_da91b.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtend.o] ==> obj [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtend.o]
        linker tool for 'CXX': D:/Qt/Qt5.13.2/Tools/mingw730_32/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib\\\\crt2.o] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/lib/crt2.o]
        collapse obj [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtbegin.o] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/crtbegin.o]
        collapse obj [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0\\\\crtend.o] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/crtend.o]
        collapse library dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib\\\\gcc\\\\i686-w64-mingw32\\\\7.3.0] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0]
        collapse library dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32\\\\lib] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/lib]
        collapse library dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\lib] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib]
        collapse library dir [D:/Qt/Qt5.13.2/Tools/mingw730_32\\\\i686-w64-mingw32/sys-root/mingw/lib] ==> [D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/sys-root/mingw/lib]
        collapse library dir [D:\\\\LLVM\\\\lib\\\\clang\\\\12.0.0\\\\lib\\\\windows] ==> [D:/LLVM/lib/clang/12.0.0/lib/windows]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;moldname;mingwex;advapi32;shell32;user32;kernel32;mingw32;gcc_s;gcc;moldname;mingwex;kernel32]
        implicit objs: [D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/lib/crt2.o;D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/crtbegin.o;D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/crtend.o]
        implicit dirs: [D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0;D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/lib;D:/Qt/Qt5.13.2/Tools/mingw730_32/lib;D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/sys-root/mingw/lib;D:/LLVM/lib/clang/12.0.0/lib/windows]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:210 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "D:/Qt/Qt5.13.2/Tools/mingw730_32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.30
...
