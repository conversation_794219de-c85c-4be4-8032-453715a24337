^E:\SOFTWARE\SORTING\BUILD-DEBUG\CMAKEFILES\5020CC0195F5B8DA341EDB35B011A542\QRC_QML.CPP.RULE
setlocal
cd E:\software\sorting\build-Debug
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\SOFTWARE\SORTING\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/software/sorting -BE:/software/sorting/build-Debug --check-stamp-file E:/software/sorting/build-Debug/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
