{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Program Files/CMake/bin/cmake.exe", "cpack": "C:/Program Files/CMake/bin/cpack.exe", "ctest": "C:/Program Files/CMake/bin/ctest.exe", "root": "C:/Program Files/CMake/share/cmake-3.29"}, "version": {"isDirty": false, "major": 3, "minor": 29, "patch": 3, "string": "3.29.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-162afdb1d564cf72adb6.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-1d988494d5b71badfca5.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6bda3e9dcc398f77fbe7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-781586acee9e3bf02156.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-1d988494d5b71badfca5.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-162afdb1d564cf72adb6.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-781586acee9e3bf02156.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6bda3e9dcc398f77fbe7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}