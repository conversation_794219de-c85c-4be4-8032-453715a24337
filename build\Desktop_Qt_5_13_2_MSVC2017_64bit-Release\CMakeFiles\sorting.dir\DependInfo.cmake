
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/software/sorting/main.cpp" "CMakeFiles/sorting.dir/main.cpp.obj" "gcc" "CMakeFiles/sorting.dir/main.cpp.obj.d"
  "E:/software/sorting/sorting.cpp" "CMakeFiles/sorting.dir/sorting.cpp.obj" "gcc" "CMakeFiles/sorting.dir/sorting.cpp.obj.d"
  "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen/EWIEGA46WW/qrc_qml.cpp" "CMakeFiles/sorting.dir/sorting_autogen/EWIEGA46WW/qrc_qml.cpp.obj" "gcc" "CMakeFiles/sorting.dir/sorting_autogen/EWIEGA46WW/qrc_qml.cpp.obj.d"
  "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen/mocs_compilation.cpp" "CMakeFiles/sorting.dir/sorting_autogen/mocs_compilation.cpp.obj" "gcc" "CMakeFiles/sorting.dir/sorting_autogen/mocs_compilation.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
