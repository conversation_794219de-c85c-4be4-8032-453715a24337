{
  "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools",
  "cmake.generator": "Ninja",
  "cmake.configureSettings": {
    "CMAKE_BUILD_TYPE": "${buildType}"
  },
  "cmake.buildDirectory": "${workspaceFolder}/build-${buildType}",
  "cmake.configureArgs": [
    // "-DDeepInspectSDK_DIR=C:/0Work/1Code/gpu/${buildType}/lib/cmake",
    "-DQt5_DIR=D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5",
    // "-DENABLE_DEFECT_MODULE=ON"
    "-DBUILD_WITH_MES=OFF"
    // "-DBUILD_BAT_FLAG=OFF"
  ],
  "files.associations": {
    "memory": "cpp",
    "filesystem": "cpp",
    "algorithm": "cpp",
    "any": "cpp",
    "array": "cpp",
    "atomic": "cpp",
    "bitset": "cpp",
    "cctype": "cpp",
    "chrono": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "codecvt": "cpp",
    "complex": "cpp",
    "condition_variable": "cpp",
    "csignal": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cstring": "cpp",
    "ctime": "cpp",
    "cwchar": "cpp",
    "deque": "cpp",
    "exception": "cpp",
    "resumable": "cpp",
    "forward_list": "cpp",
    "fstream": "cpp",
    "functional": "cpp",
    "future": "cpp",
    "initializer_list": "cpp",
    "iomanip": "cpp",
    "ios": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "iterator": "cpp",
    "limits": "cpp",
    "list": "cpp",
    "locale": "cpp",
    "map": "cpp",
    "mutex": "cpp",
    "new": "cpp",
    "numeric": "cpp",
    "optional": "cpp",
    "ostream": "cpp",
    "queue": "cpp",
    "random": "cpp",
    "ratio": "cpp",
    "regex": "cpp",
    "set": "cpp",
    "shared_mutex": "cpp",
    "sstream": "cpp",
    "stack": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "string": "cpp",
    "string_view": "cpp",
    "system_error": "cpp",
    "xthread": "cpp",
    "thread": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "typeindex": "cpp",
    "typeinfo": "cpp",
    "unordered_map": "cpp",
    "unordered_set": "cpp",
    "utility": "cpp",
    "valarray": "cpp",
    "variant": "cpp",
    "vector": "cpp",
    "xfacet": "cpp",
    "xhash": "cpp",
    "xiosbase": "cpp",
    "xlocale": "cpp",
    "xlocbuf": "cpp",
    "xlocinfo": "cpp",
    "xlocmes": "cpp",
    "xlocmon": "cpp",
    "xlocnum": "cpp",
    "xloctime": "cpp",
    "xmemory": "cpp",
    "xmemory0": "cpp",
    "xstddef": "cpp",
    "xstring": "cpp",
    "xtr1common": "cpp",
    "xtree": "cpp",
    "xutility": "cpp",
    "qdatetime": "cpp",
    "qobject": "cpp",
    "charconv": "cpp",
    "qdebug": "cpp",
    "qstandardpaths": "cpp"
  }
}