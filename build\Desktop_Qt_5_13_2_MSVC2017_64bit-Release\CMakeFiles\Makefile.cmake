# CMAKE generated file: DO NOT EDIT!
# Generated by "NMake Makefiles JOM" Generator, CMake Version 3.29

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "NMake Makefiles JOM")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCompilerIdDetection.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompileFeatures.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineRCCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeFindBinUtils.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeGenericSystem.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeJOMFindMake.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitLinkInfo.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseLibraryArchitecture.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCCompiler.cmake.in"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystem.cmake.in"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestRCCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-FindBinUtils.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TI-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/FeatureTesting.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Clang-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Clang.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Determine-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Initialize.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-windres.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows.cmake"
  "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/WindowsPaths.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake"
  "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake"
  "E:/software/sorting/CMakeLists.txt"
  ".qtc/package-manager/auto-setup.cmake"
  "CMakeFiles/3.29.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.29.3/CMakeRCCompiler.cmake"
  "CMakeFiles/3.29.3/CMakeSystem.cmake"
  "E:/software/sorting/qml.qrc"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.29.3/CMakeSystem.cmake"
  "CMakeFiles/3.29.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.29.3/CMakeRCCompiler.cmake"
  "CMakeFiles/3.29.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/sorting_autogen.dir/AutogenInfo.json"
  "CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/sorting.dir/DependInfo.cmake"
  "CMakeFiles/sorting_autogen.dir/DependInfo.cmake"
  )
