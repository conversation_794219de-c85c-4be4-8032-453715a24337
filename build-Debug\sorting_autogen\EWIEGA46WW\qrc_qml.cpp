/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.13.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // E:/software/sorting/main.qml
  0x0,0x0,0x8,0x7,
  0x0,
  0x0,0x34,0x5d,0x78,0x9c,0xed,0x5b,0x5b,0x6f,0xd4,0x46,0x14,0x7e,0xcf,0xaf,0x70,
  0xb7,0xaa,0xd8,0x28,0x74,0xf1,0x26,0x80,0xc2,0xd2,0xa8,0x82,0x0,0x2,0xa9,0x91,
  0xa,0x5d,0x91,0x7,0xc4,0x83,0xb3,0x9e,0xdd,0x58,0xf5,0xda,0x5b,0xdb,0x9b,0xa4,
  0x54,0x2b,0xc1,0x3,0x65,0x21,0x1,0xa2,0xb6,0x34,0xad,0x54,0x48,0x2,0xa2,0xa4,
  0x2a,0x4a,0x90,0x80,0x24,0x4a,0xd2,0xe6,0xcf,0xc4,0xce,0xe6,0x89,0xbf,0xd0,0x39,
  0xe3,0xcb,0x7a,0x3d,0xe3,0xcb,0x6e,0x2e,0x25,0x88,0xcf,0x12,0x59,0x8f,0xcf,0x5c,
  0x7c,0xbe,0x73,0xce,0x9c,0x99,0x31,0x52,0xb9,0xa2,0x6a,0x6,0x77,0xd9,0xb8,0x5c,
  0x95,0xa,0xdf,0x72,0xbd,0x99,0x6c,0x6f,0x97,0xd4,0x52,0x96,0x19,0x96,0x14,0x51,
  0x1d,0x67,0x3e,0x1a,0x54,0x15,0x43,0x53,0x65,0x9d,0xf9,0xf0,0x2b,0xe1,0x7b,0xb5,
  0x6a,0xe8,0x5c,0x16,0x9e,0x75,0x39,0xad,0xfc,0xd0,0xc5,0x61,0x8c,0x49,0xba,0x34,
  0x22,0xa3,0x1c,0x67,0x68,0x55,0x44,0x4a,0xc6,0x25,0xd1,0x18,0xcd,0x71,0xa7,0x78,
  0x9e,0xdc,0x8e,0x22,0xa9,0x34,0x6a,0xe4,0xb8,0x93,0xce,0xbd,0x21,0x19,0x20,0xfe,
  0x9d,0x9e,0xd7,0xd2,0xa9,0xc6,0x9b,0x85,0x9d,0x3b,0xf,0xad,0x67,0x37,0xad,0xb7,
  0x93,0x5b,0xab,0xf,0xcc,0xfa,0x8f,0xd6,0xe4,0xb3,0xed,0xd7,0xeb,0xdb,0xeb,0xb3,
  0xa9,0x6e,0x22,0x4e,0xfe,0x39,0x76,0x8c,0x6b,0x6c,0x3e,0xb1,0x1e,0x3c,0x1f,0xec,
  0xe9,0xd9,0xda,0x9c,0x37,0x1f,0xae,0x90,0x62,0x3c,0x66,0x5,0x15,0xc,0x49,0x55,
  0x74,0x67,0x34,0xa4,0x3,0x41,0x2b,0x21,0xdc,0xa1,0x8e,0xdf,0x40,0x52,0x4a,0x43,
  0x82,0x22,0x94,0x90,0xe6,0x3d,0x56,0x95,0x21,0xa4,0xeb,0xb8,0xe8,0x8c,0x28,0x22,
  0x31,0xe7,0xab,0x8,0x28,0xdb,0xcf,0x86,0x54,0x11,0xc9,0x19,0x49,0xd1,0x91,0x66,
  0xa4,0xf9,0xa3,0x1,0x21,0x40,0xca,0x91,0x4c,0xe5,0xdc,0x3a,0x47,0x69,0x19,0x43,
  0x2a,0x83,0x0,0xfc,0xd1,0xd,0xa1,0x5c,0x69,0x91,0xa8,0x75,0x7b,0xb7,0x35,0xdf,
  0xe8,0xce,0x9,0x86,0x70,0x89,0xa8,0x9f,0x1e,0x1d,0xd6,0x83,0xf5,0xe8,0x95,0x75,
  0x7f,0xd1,0x5c,0xda,0x30,0x6f,0x3f,0x37,0x17,0xa7,0xac,0xfa,0xb4,0x39,0x8d,0xf5,
  0xb6,0x62,0xfd,0xfa,0xaa,0xb1,0xf8,0xca,0xfc,0xe7,0x91,0x59,0x9f,0x69,0xcc,0x2f,
  0xb4,0x54,0xd3,0x50,0x41,0xd5,0x44,0xfb,0x9d,0xa,0x32,0x12,0xb4,0x74,0x77,0xcb,
  0xf3,0x31,0x41,0x73,0x64,0x74,0x6e,0x20,0xa0,0xb7,0xc,0xd6,0xe5,0x39,0x64,0xd8,
  0x6a,0xbe,0x62,0xb,0x5,0xaa,0x17,0x55,0x8d,0x4b,0x43,0x1b,0x12,0xae,0xcd,0x9f,
  0xc6,0x7f,0xbe,0x70,0x9b,0xcb,0xc8,0x48,0x29,0x19,0xa3,0xb8,0xac,0xa7,0xa7,0x9b,
  0xa1,0x45,0xff,0xc8,0x84,0x4a,0x5,0x29,0x62,0xda,0xa9,0x79,0x4d,0xba,0xde,0xda,
  0x4b,0x8d,0xa9,0x2d,0x7b,0x44,0x4c,0x2a,0xfd,0x6d,0x37,0x99,0xb4,0x4b,0x83,0xaa,
  0xaf,0x35,0x6d,0xed,0x8a,0x3a,0x6e,0x9b,0xbb,0xaf,0x39,0x41,0x29,0x8c,0xaa,0x9a,
  0x9e,0x29,0x4a,0xb2,0x9c,0xe3,0x2a,0x82,0x86,0x14,0xc3,0x7b,0xa8,0x57,0x84,0x2,
  0xd6,0x57,0x8e,0x3b,0x41,0xc9,0x97,0xb1,0x29,0xe2,0xae,0xfd,0x8f,0xbc,0x1f,0x98,
  0x4b,0x73,0xe5,0xcf,0xad,0x7f,0x5f,0xec,0x3c,0x7e,0x6a,0x3d,0xde,0xe4,0x3e,0xe7,
  0xac,0xe5,0xba,0x75,0x6b,0xc9,0x66,0xd1,0x13,0x1b,0x54,0xe5,0x6a,0x59,0xa1,0x46,
  0x4,0xb0,0xb,0xc9,0xa0,0x2e,0x3a,0x4e,0xe6,0x39,0x61,0x40,0xa6,0xa2,0xa1,0x22,
  0xd2,0x34,0x24,0xe,0xdb,0xce,0x79,0xdc,0x71,0x46,0xea,0x1d,0xf8,0xae,0x96,0x72,
  0xac,0x5e,0x43,0x50,0x4a,0x32,0x62,0x70,0xe7,0xeb,0xde,0x69,0x95,0xea,0x1d,0xe0,
  0xfa,0x7f,0x1f,0x4f,0x3d,0x2a,0xa8,0xb2,0xaa,0xe5,0xb8,0xd4,0xa7,0x88,0x87,0x2b,
  0x45,0x9,0x8c,0x60,0xaa,0xb0,0x9,0x7a,0x72,0x5,0x1e,0xae,0x54,0x17,0x25,0x98,
  0x47,0x13,0x41,0xed,0xb8,0x70,0xb9,0x28,0x60,0xce,0x90,0x76,0x49,0xa1,0xf8,0xf3,
  0xc3,0xc0,0xed,0xe0,0x8e,0xfc,0x4c,0xd0,0xa3,0x2,0x14,0x71,0xb8,0xcc,0x8c,0xa8,
  0xb2,0x18,0xf2,0xd6,0x9e,0x4c,0x45,0x9a,0x40,0xf2,0x37,0xd2,0xd,0x1c,0xf2,0xb2,
  0xc7,0x29,0xb1,0x5a,0xc0,0xc4,0x5b,0xb9,0x93,0x74,0xe3,0xaa,0x84,0xc6,0x19,0x2f,
  0x26,0x89,0x5e,0xd8,0x1,0xa9,0x4e,0xb9,0x49,0x62,0x41,0x3e,0x39,0xcf,0x9c,0x7b,
  0x69,0x2a,0x65,0xa9,0x12,0x52,0xd9,0xb3,0x2d,0xba,0xd6,0x18,0x76,0x4a,0xa9,0x20,
  0xc8,0x76,0xfb,0xe7,0x24,0xcd,0xe,0x33,0x39,0xef,0xd5,0x33,0x67,0x55,0xc3,0x50,
  0xcb,0x79,0x35,0xaf,0x56,0x68,0xd6,0xcb,0xe0,0xdb,0xb6,0x30,0x71,0xf3,0x10,0xb,
  0xf0,0x29,0x8b,0x88,0xc5,0xb0,0x0,0xa0,0xa,0x70,0x35,0x54,0x12,0xc,0xcc,0x62,
  0x94,0x4b,0x0,0x9c,0xe9,0xcf,0xc7,0x4e,0x86,0x14,0x31,0x85,0x5d,0xe7,0x18,0x12,
  0x8c,0x51,0xac,0xde,0x89,0x74,0x1f,0xe,0x51,0x4e,0x55,0xb0,0x69,0x6c,0xfa,0xd8,
  0x6a,0x15,0xc3,0x66,0x87,0xeb,0xe1,0xb2,0x7c,0x37,0xb3,0x21,0xcf,0x45,0x8a,0xfd,
  0x70,0xb1,0x8d,0x36,0xe8,0x4e,0x61,0x6e,0x7,0xd0,0x4,0x51,0xaa,0x62,0xaa,0xfb,
  0x68,0xb5,0x3,0x22,0x1c,0xe,0xe0,0x53,0x39,0x48,0x86,0xca,0xb9,0xce,0x29,0xa3,
  0xa2,0xe1,0x3a,0x26,0xb9,0x89,0xad,0xa1,0x39,0xe6,0x8a,0xa7,0x56,0xa2,0xa8,0x44,
  0x95,0xc,0xb5,0xe2,0xf5,0x82,0x7f,0xc7,0xca,0x8f,0x10,0xeb,0xf3,0xaa,0xd8,0xb7,
  0xb1,0xb5,0x18,0x51,0x3f,0x8,0x3b,0xce,0x38,0x1a,0xa,0x95,0xf2,0x88,0xea,0x23,
  0x60,0x13,0x5,0x18,0xd7,0x84,0xa,0x98,0x76,0x8e,0xf0,0x92,0x19,0xc6,0x3c,0xf,
  0xe3,0xa2,0x50,0x79,0x2a,0x32,0x65,0x43,0x45,0x5d,0x17,0x3d,0x23,0x4b,0x25,0xa5,
  0x8c,0xb5,0xe0,0xf4,0x41,0xee,0xaf,0xe,0x92,0xa0,0xca,0xac,0x4c,0x3b,0x15,0xa0,
  0x53,0x63,0x72,0x79,0x4e,0x6a,0x17,0xe,0x63,0xe4,0x6e,0xcf,0xcd,0x22,0x31,0xc1,
  0x30,0xea,0x78,0x76,0x4f,0x12,0x84,0xb3,0x4b,0xb1,0x45,0xcf,0xa4,0x0,0x5a,0xe1,
  0xc1,0xd9,0xc5,0x7f,0x47,0xe5,0x95,0xf,0x5e,0x98,0xf5,0x65,0x6b,0xea,0xee,0xce,
  0x4f,0x8b,0xe6,0xd4,0x9a,0x39,0x3b,0xbb,0x5f,0x89,0x40,0xb6,0x37,0x22,0x13,0x28,
  0xf2,0x70,0xc5,0x67,0x2,0x61,0xa1,0xcb,0xd,0x5b,0x27,0xe8,0xb0,0x15,0x91,0x49,
  0xb9,0x88,0xcc,0xf3,0x58,0x82,0x9e,0x25,0x84,0x70,0xe2,0xcd,0x7d,0xfd,0xec,0x30,
  0x8a,0x15,0xbf,0xfd,0xf2,0xe5,0xd6,0xea,0xcd,0xc6,0xfc,0x94,0xad,0x7b,0xa6,0x18,
  0x2b,0x27,0xd,0x22,0x21,0xb,0xd4,0xc8,0xb2,0x3c,0x7b,0x68,0x80,0xb3,0x55,0x1c,
  0xf0,0x94,0x88,0x4e,0x1,0xb6,0xa9,0xdb,0xeb,0x39,0x7b,0x5d,0x62,0xaf,0x51,0x52,
  0xec,0x99,0x2a,0x30,0xde,0x60,0x66,0x9a,0xe5,0xd9,0x9a,0x74,0xa1,0x2a,0x83,0x32,
  0x5e,0x92,0xd2,0xe9,0x3e,0xb,0xb0,0x28,0xc1,0xa,0x41,0x5f,0xe3,0xe9,0x95,0x5e,
  0xd9,0xe8,0x78,0x4e,0x2f,0x18,0xf6,0x5a,0xeb,0x2,0x96,0x4a,0x47,0xf,0x18,0x20,
  0x15,0xb9,0xb4,0xd7,0xe0,0x27,0x3,0x3,0x5c,0x2a,0xc5,0x5a,0xd6,0xb0,0x10,0xe8,
  0xdc,0x5e,0x61,0xc3,0x62,0xef,0x82,0xa6,0x96,0x49,0xf7,0x6e,0xc3,0xf1,0xc3,0x60,
  0xc7,0xd6,0xf8,0xa7,0xb5,0x3d,0xa4,0xba,0xb9,0x10,0xbd,0xb3,0x76,0x48,0xa8,0x3e,
  0x3f,0x1,0x3a,0x7,0x89,0x3,0xa5,0x1a,0x4d,0xb8,0x54,0xe7,0xd5,0x41,0x7d,0xec,
  0x40,0x78,0x66,0x97,0x46,0xc5,0xa0,0xb5,0xa9,0xf,0x24,0x6,0x59,0xab,0xb7,0xb7,
  0xff,0x5a,0x73,0x16,0x6f,0xff,0xbf,0x61,0x6,0x8c,0x81,0xec,0xbf,0xb0,0xf7,0x51,
  0x58,0x88,0xdb,0xba,0x9,0xe2,0x60,0x7c,0x7f,0xfb,0x97,0x59,0xab,0x3e,0x6d,0xbd,
  0x9d,0x6c,0x2c,0x3d,0xda,0x55,0xb0,0x67,0xa4,0x2,0x7e,0xec,0x46,0xd1,0x25,0xa4,
  0x20,0xd,0xaf,0xd8,0xf2,0x48,0x27,0xbe,0xb7,0xb,0xc5,0xb1,0x4b,0x23,0x27,0xf4,
  0xbb,0x1f,0x88,0x33,0x35,0x36,0xa7,0xe1,0x4d,0x8,0xd3,0xef,0x9f,0x33,0x69,0x55,
  0x5,0xe8,0x8d,0x73,0xa4,0xf6,0xb9,0x8d,0x2e,0x61,0x6d,0x45,0x7a,0x3f,0x60,0x5f,
  0xef,0xe1,0xeb,0x96,0x7d,0x3d,0xb2,0xbd,0xbd,0x2f,0xfb,0x7a,0x51,0xc6,0xe1,0xdb,
  0xd3,0xf3,0x17,0xef,0x57,0x5a,0x7f,0xa8,0xf6,0xf7,0x7c,0x8c,0x1c,0xe8,0xfe,0x9e,
  0xff,0x2e,0xb8,0x2,0x6b,0xcc,0x2f,0x58,0x73,0x1b,0xd6,0xdc,0x9d,0x9d,0xa7,0xbf,
  0x61,0x9f,0xdb,0x37,0x9e,0xe8,0x45,0x6b,0x73,0xf5,0x75,0x2,0xae,0x78,0x9e,0x44,
  0x1e,0x2e,0x5a,0x8e,0x2a,0x88,0x8b,0x73,0x9d,0x2f,0xb8,0xd8,0x4b,0xef,0x66,0x10,
  0xdc,0xb3,0xdd,0x87,0xe,0xa6,0x2e,0xd7,0xca,0x66,0x96,0x77,0x66,0xde,0x58,0xf5,
  0xd7,0x31,0x4b,0xfb,0x18,0x33,0xf3,0xe4,0xfc,0xa6,0x46,0xef,0xa5,0xba,0xc0,0x6a,
  0x92,0x6e,0x60,0xf1,0x90,0x2d,0x9b,0x8b,0x6d,0x6f,0xd9,0xf8,0x6c,0xcf,0xdd,0xdc,
  0xcc,0x9e,0xe,0xb,0x52,0xa7,0xb9,0xa0,0x91,0x84,0xb4,0xda,0x99,0xd6,0xfb,0xe3,
  0x95,0xfe,0x78,0x7e,0x7b,0xee,0xd6,0x47,0x8d,0xd3,0xe8,0x4c,0xe3,0x27,0x63,0x35,
  0xbe,0xbd,0xfe,0xb3,0xf5,0xe4,0x8f,0x8f,0x1a,0xa7,0x91,0x4c,0xe3,0x49,0x13,0x3c,
  0x57,0xdd,0x1b,0x6b,0x3b,0xbf,0xaf,0x6c,0x6d,0xce,0x5b,0xb7,0x96,0xe,0xaf,0xd2,
  0x13,0x4f,0x8c,0x31,0x87,0x60,0xf6,0xfa,0xe8,0xf0,0x9c,0x81,0xd1,0xf3,0x12,0x55,
  0xd0,0xc6,0xa9,0x96,0x6f,0x75,0x18,0xa3,0x61,0x66,0x4f,0x6d,0x1f,0x6a,0x35,0xb5,
  0x9d,0xe0,0x4c,0x8b,0x91,0x10,0x2,0x1c,0xdf,0x91,0x14,0x11,0x4d,0x70,0x9f,0x71,
  0xbd,0xdc,0xc0,0xc0,0x0,0xc7,0x73,0x5f,0x42,0xfa,0x41,0x90,0xe2,0x48,0x2a,0x72,
  0xa,0xae,0xdd,0x9f,0x63,0x75,0xbc,0xfc,0x4a,0x9c,0x9a,0xf8,0x85,0x13,0x9c,0xc,
  0xc4,0xa4,0x28,0xa1,0x63,0x6,0xc4,0x4,0x14,0x40,0x87,0x2b,0xed,0xe6,0x79,0x5,
  0xfd,0xa1,0x4a,0x10,0x9,0xcf,0x23,0x5c,0xec,0x22,0x6e,0xb8,0xe8,0xf8,0x10,0xa,
  0x10,0xbe,0xfa,0xeb,0x34,0xea,0x3b,0xf6,0x16,0xd1,0x72,0xe7,0x44,0x45,0x64,0x38,
  0x0,0x9b,0xa7,0x11,0x41,0x2b,0x60,0xa7,0x6f,0x8f,0xa5,0x70,0x7b,0x3,0x7c,0x64,
  0x89,0x89,0xf6,0xb3,0x22,0x80,0xcd,0x92,0xd8,0xfc,0x6c,0x4a,0xaf,0xca,0xe1,0xc1,
  0x3,0xd0,0x26,0x5b,0xce,0x4b,0x6,0x7a,0x20,0xa1,0x34,0x65,0x4e,0xd7,0xf1,0x3a,
  0x32,0x45,0x22,0x2a,0xcf,0xf7,0xf3,0x3c,0xef,0x44,0xd4,0x22,0xf,0xbf,0xdf,0x6b,
  0x2b,0xf0,0x54,0x91,0x20,0x75,0x79,0xbf,0x2d,0x26,0x69,0x56,0x7,0x70,0xcd,0xa5,
  0x8,0xc7,0x51,0x4a,0x51,0xfd,0x90,0xfc,0x1a,0x80,0x64,0xc9,0xfb,0x36,0xe1,0x3c,
  0xfc,0xbe,0x12,0x79,0x2c,0xdf,0xe9,0xa6,0x5c,0x70,0x43,0xc5,0xba,0x3b,0x69,0xde,
  0x5b,0xb0,0x56,0xd6,0xcd,0x7b,0x73,0xce,0xb7,0x91,0xfb,0x7a,0xb0,0xcd,0x8,0xa,
  0xb1,0x9f,0xe5,0xb4,0x7b,0xae,0xcd,0xf8,0x1c,0x67,0x5f,0xcf,0xb5,0xfb,0xa3,0xb7,
  0x59,0x18,0xc7,0xec,0x80,0x18,0x37,0x71,0x97,0xea,0x3e,0x7e,0xe2,0x77,0xe4,0x0,
  0x7b,0xbc,0xaa,0x9,0xd9,0xc3,0xdf,0xcf,0xcd,0xf9,0x10,0x85,0x1,0x40,0x69,0x17,
  0x24,0x24,0x8b,0x31,0x1,0x6,0x12,0x7f,0x27,0x1,0xb8,0xa4,0x54,0xaa,0xd1,0xf3,
  0x4a,0x67,0x59,0x46,0x45,0x16,0xa,0x68,0x14,0xeb,0x19,0x69,0xf9,0x64,0xdb,0x2a,
  0x80,0x36,0x22,0x53,0xc4,0x61,0xd4,0xa0,0x5a,0x1e,0x51,0xcf,0xaa,0x13,0x9,0xb4,
  0xa0,0x91,0x59,0x8f,0x54,0xd8,0x7,0x25,0x38,0xeb,0xb0,0x6b,0xee,0x84,0x7a,0x94,
  0x4b,0x6d,0xad,0xde,0x77,0x6e,0xae,0x1f,0x80,0x26,0xda,0x31,0x8,0x77,0xf2,0x48,
  0x68,0xf,0xed,0xcc,0x4e,0xb4,0x31,0xf8,0xb7,0x20,0xde,0x6d,0xd4,0x6d,0x95,0x58,
  0x33,0xcb,0xe6,0xfc,0xdf,0x47,0xac,0x99,0xb9,0x23,0xef,0x36,0xee,0x1e,0x84,0xa5,
  0xb4,0x7f,0x32,0x4c,0x42,0x4d,0x67,0xc7,0x58,0x31,0x19,0x5f,0x5b,0xa7,0x58,0xf0,
  0x69,0x81,0xdf,0x83,0x33,0x30,0xc8,0x36,0xbf,0x31,0x80,0xef,0x1d,0x6c,0xd2,0xb9,
  0x1,0x3f,0xfb,0x76,0x5b,0x24,0x11,0x24,0x29,0x20,0xe6,0x3,0xd2,0xbf,0xa0,0x44,
  0xa2,0x3e,0x2,0xa7,0x6d,0x82,0x28,0x6,0xfe,0x27,0x40,0x3a,0x51,0x33,0x0,0xea,
  0x75,0xe9,0xff,0x2d,0x11,0x6,0x9f,0x97,0x67,0xa,0x55,0xd,0xe6,0xae,0x7c,0x5b,
  0xd,0xd8,0xef,0x9e,0x48,0x3c,0xfe,0x40,0x1e,0x40,0x73,0x7,0xda,0x4e,0x54,0x95,
  0x66,0x2a,0x49,0xcd,0xbd,0xfe,0xe,0x24,0xba,0x24,0x78,0xbc,0x59,0xeb,0xaa,0x75,
  0xfd,0x7,0xac,0x9d,0x68,0xb0,
  
};

static const unsigned char qt_resource_name[] = {
  // main.qml
  0x0,0x8,
  0x8,0x1,0x5a,0x5c,
  0x0,0x6d,
  0x0,0x61,0x0,0x69,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/main.qml
  0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0xa2,0x49,0xfa,0x70,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)(); }
   } dummy;
}
