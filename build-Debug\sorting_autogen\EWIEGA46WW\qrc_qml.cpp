/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.13.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // E:/software/sorting/main.qml
  0x0,0x0,0x3,0x2a,
  0x0,
  0x0,0xc,0x50,0x78,0x9c,0xed,0x56,0x4b,0x6f,0xd3,0x40,0x10,0xbe,0x57,0xea,0x7f,
  0xb0,0xcc,0x25,0x15,0x95,0xe5,0x94,0x80,0x2a,0xdf,0x68,0x7a,0x0,0x29,0x1c,0xda,
  0x46,0xcd,0xd9,0x75,0x36,0xce,0x8a,0xf5,0xae,0x59,0xaf,0x9b,0xf2,0xc8,0x1,0x24,
  0x68,0x81,0x22,0x21,0xa4,0x72,0x43,0x3d,0x21,0x6e,0x6d,0x4f,0x48,0xb4,0x20,0xfe,
  0xc,0xe,0x70,0xe2,0x2f,0x30,0xeb,0x47,0xe2,0xd8,0xeb,0xd0,0x8a,0x5e,0x90,0xf8,
  0x5c,0xa9,0xde,0xd9,0x6f,0x67,0x66,0xe7,0xe5,0xfc,0x3a,0xfb,0x8a,0x3d,0x9f,0x71,
  0xa1,0xad,0x89,0xb5,0x10,0x3b,0x77,0xb5,0x25,0xa3,0xbe,0x34,0x3f,0x37,0x2d,0x34,
  0x3a,0x98,0x76,0xd9,0x40,0xbd,0xd7,0x64,0x54,0x70,0x46,0x2,0xf5,0x6e,0xcb,0xbe,
  0xcf,0x42,0x11,0x68,0xf5,0x78,0x73,0x7e,0x2e,0xd5,0xf4,0x70,0x7e,0x4e,0x3,0x6c,
  0xe3,0x0,0x6f,0x11,0x64,0x69,0x82,0x87,0x28,0x11,0xd,0x70,0x57,0xf4,0x2d,0xed,
  0x46,0xc3,0x4c,0xd6,0x7d,0x84,0xdd,0xbe,0xb0,0xb4,0xc6,0x72,0x2a,0x10,0x58,0xc8,
  0x13,0xf7,0x82,0x36,0xaf,0xe9,0x1b,0x60,0xb,0x53,0x57,0x5f,0x48,0xf6,0x9a,0x8c,
  0x84,0x1e,0x4d,0x6c,0x66,0x36,0x24,0x6c,0xea,0xf4,0x19,0xf,0x8c,0x1e,0x26,0xc4,
  0xd2,0x7c,0x9b,0x23,0x2a,0x26,0xbb,0x81,0x6f,0x3b,0xa0,0xc4,0xd2,0x4c,0xe9,0x61,
  0x26,0x5d,0x47,0x8e,0xb0,0xa9,0x4b,0x50,0x5e,0x91,0x44,0xa2,0x3e,0xd6,0xd5,0x49,
  0x9c,0x9d,0x78,0x9f,0x21,0xf3,0x7a,0xe9,0xfa,0xb4,0xdc,0x61,0x84,0x71,0x4b,0xd3,
  0xaf,0x20,0x53,0x3e,0x7a,0xde,0xa0,0x44,0x1b,0xed,0x88,0xa2,0xbd,0xfc,0x5,0x1c,
  0x70,0x1c,0xf1,0xdb,0xb4,0x7c,0x89,0xc,0x2,0x34,0x80,0x81,0xd1,0xc7,0xbd,0xd1,
  0xe3,0xe3,0x1f,0x47,0x27,0xd1,0x97,0x3,0xbd,0xcc,0xea,0x41,0xd2,0x8c,0x2d,0x46,
  0xba,0x2a,0xe7,0x87,0x93,0xe5,0x30,0xef,0x60,0xb,0x7,0x62,0x13,0xa3,0x41,0xd1,
  0x41,0xc,0x5a,0x3c,0x14,0x4,0xb6,0x8b,0x24,0xe5,0xe2,0xd1,0xca,0x71,0x6e,0xa5,
  0x81,0xab,0x24,0x79,0x36,0x77,0x31,0xd,0x2c,0xad,0x18,0x59,0x82,0x7d,0xd5,0xb1,
  0x71,0x72,0x1b,0xd3,0xf2,0x6d,0x4,0x95,0xe3,0xd8,0x24,0x51,0xbb,0x8a,0x39,0xa4,
  0x1b,0x33,0x8,0x6c,0x76,0x4b,0x63,0x85,0x9,0xc1,0xbc,0x36,0x6b,0x33,0xbf,0x98,
  0x26,0x8f,0x75,0x11,0x49,0xa8,0x77,0xe4,0xab,0x2a,0x65,0xb9,0xa8,0xc4,0x9c,0x32,
  0xa3,0xc9,0xa0,0x57,0x28,0x64,0xd1,0x60,0x54,0xbe,0x13,0x24,0x10,0x1c,0x52,0xe8,
  0x8a,0xf5,0xd1,0x0,0x5c,0xae,0x99,0x8b,0x55,0x84,0x1c,0xf4,0xd4,0xb0,0x5e,0xa8,
  0x84,0x68,0xff,0x34,0x3a,0x3c,0x8c,0xf6,0xde,0x45,0x1f,0x5e,0x46,0xfb,0x6f,0x47,
  0x7b,0xaf,0xa3,0x17,0x87,0xfa,0xe2,0x9f,0xf5,0x9,0xec,0x49,0x65,0x14,0x92,0xbf,
  0x6a,0xb,0x54,0x5b,0x30,0x4,0x6b,0x31,0x8,0x1f,0x6a,0xc3,0xce,0x86,0xe0,0x10,
  0xe2,0xda,0xc2,0x4c,0x3d,0x43,0xc5,0xf6,0xb0,0xb2,0xf0,0x24,0x20,0x68,0xc8,0x5,
  0x6b,0x56,0x75,0x2f,0x4a,0xa4,0x13,0x23,0x57,0x81,0x46,0x2c,0x2a,0x33,0xb3,0xae,
  0x4c,0xa9,0xb2,0xd9,0xc,0x7,0x3a,0x1,0x52,0x90,0xd4,0x9d,0x76,0x55,0xab,0x9b,
  0xe5,0x73,0xe3,0xae,0xed,0x2d,0xcb,0x47,0xd1,0x4f,0x5b,0x8c,0x77,0x11,0x37,0xca,
  0xed,0x5d,0x24,0x72,0xbb,0x8b,0xc3,0x20,0xae,0xc6,0xf2,0x66,0x55,0xf7,0x4b,0xe4,
  0xca,0x49,0xd2,0xd4,0xa4,0xd9,0x73,0x4e,0xc5,0xac,0x6a,0xa7,0xc,0xc9,0x38,0x49,
  0xd,0xab,0x29,0xe3,0x4b,0x5f,0x8b,0xa1,0xb8,0xb4,0xc4,0x80,0xdb,0xbe,0xec,0x3,
  0x2b,0xbe,0xa5,0xd1,0x81,0x80,0x75,0x40,0xa4,0x26,0xc7,0xe3,0xc9,0xc7,0x3b,0x88,
  0x6c,0xe0,0x7,0x70,0x44,0x7e,0x3b,0x8a,0x9c,0xea,0xda,0xc9,0xbd,0x5e,0xca,0x10,
  0x6f,0x14,0x4a,0x62,0x52,0xe,0xa6,0x7c,0xa,0x17,0xae,0x2c,0x85,0x69,0xda,0x3a,
  0x1b,0x94,0xbf,0x53,0x19,0xce,0x97,0xc7,0xf3,0xe4,0x70,0x3c,0xfe,0xea,0xa6,0xaa,
  0xe2,0x56,0x42,0x98,0x71,0xb4,0xaa,0xe6,0x92,0xe4,0x27,0x5f,0xda,0xe8,0xf8,0x73,
  0xf4,0xf4,0xfd,0xe8,0xe0,0x64,0xf4,0xea,0x48,0xaf,0xe8,0xf4,0x34,0xa8,0x36,0xc1,
  0x2e,0xf5,0xc0,0x65,0xb,0x7e,0x6,0x18,0x37,0xe5,0xaa,0x85,0x7a,0x42,0x7b,0x34,
  0x5e,0x6e,0x36,0xe3,0xef,0xd8,0x4c,0x2d,0x1e,0xa6,0xd8,0xb,0xbd,0x34,0x3b,0x75,
  0x53,0xd1,0x97,0x12,0x30,0x3a,0x9,0xfc,0xd0,0x98,0x31,0x38,0x25,0xf2,0x83,0xd8,
  0xb8,0xc8,0x24,0x2d,0x21,0x3f,0x5a,0xbf,0x3f,0xf9,0x14,0xed,0x9e,0x7d,0x3b,0x7d,
  0x96,0xf,0xce,0x68,0xff,0xf9,0xcf,0x37,0x47,0xe7,0x18,0xaa,0x65,0xd5,0x97,0x30,
  0x65,0x4b,0x50,0x8d,0xdd,0x58,0xae,0xec,0xa8,0xbf,0xa9,0x90,0xe4,0xfa,0x32,0x14,
  0xbb,0xa7,0xff,0x2b,0xa4,0x50,0x21,0x53,0xc1,0xf9,0x37,0x6b,0xa3,0x62,0x99,0xbe,
  0xc2,0x3f,0xf8,0xfb,0xd,0x3d,0x3,0x1,0x5f,
  
};

static const unsigned char qt_resource_name[] = {
  // main.qml
  0x0,0x8,
  0x8,0x1,0x5a,0x5c,
  0x0,0x6d,
  0x0,0x61,0x0,0x69,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/main.qml
  0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0xa1,0xf9,0x71,0x40,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)(); }
   } dummy;
}
