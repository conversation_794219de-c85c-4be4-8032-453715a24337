[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++11", "-fansi-escape-codes", "-fcolor-diagnostics", "-fsyntax-only", "-m64", "--target=i686-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "-IE:\\software\\sorting", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\\sorting_autogen\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtCore", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\mkspecs\\win32-msvc", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQuick", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQml", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtNetwork", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtGui", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtANGLE", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\i686-w64-mingw32", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\qtcreator-15.0.0\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\i686-w64-mingw32\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\software\\sorting\\main.cpp"], "directory": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/.qtc_clangd", "file": "E:/software/sorting/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++11", "-fansi-escape-codes", "-fcolor-diagnostics", "-fsyntax-only", "-m64", "--target=i686-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "-IE:\\software\\sorting", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\\sorting_autogen\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtCore", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\mkspecs\\win32-msvc", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQuick", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQml", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtNetwork", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtGui", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtANGLE", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\i686-w64-mingw32", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\qtcreator-15.0.0\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\i686-w64-mingw32\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "E:\\software\\sorting\\sorting.cpp"], "directory": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/.qtc_clangd", "file": "E:/software/sorting/sorting.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++11", "-fansi-escape-codes", "-fcolor-diagnostics", "-fsyntax-only", "-m64", "--target=i686-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\qtcreator-15.0.0\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "-IE:\\software\\sorting", "-IE:\\software\\sorting\\build\\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\\sorting_autogen\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtCore", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\mkspecs\\win32-msvc", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQuick", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtQml", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtNetwork", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtGui", "-isystem", "D:\\Qt\\Qt5.13.2\\5.13.2\\msvc2017_64\\include\\QtANGLE", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\i686-w64-mingw32", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\lib\\gcc\\i686-w64-mingw32\\7.3.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\qtcreator-15.0.0\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\i686-w64-mingw32\\include", "-isystem", "D:\\Qt\\Qt5.13.2\\Tools\\mingw730_32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "E:\\software\\sorting\\sorting.h"], "directory": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/.qtc_clangd", "file": "E:/software/sorting/sorting.h"}]