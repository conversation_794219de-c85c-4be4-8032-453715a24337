﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\software\sorting\build-Debug\sorting_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\software\sorting\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\software\sorting\sorting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\software\sorting\build-Debug\sorting_autogen\EWIEGA46WW\qrc_qml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\software\sorting\build-Debug\sorting_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\software\sorting\build-Debug\sorting_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\software\sorting\build-Debug\sorting_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\software\sorting\sorting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\software\sorting\build-Debug\CMakeFiles\5020cc0195f5b8da341edb35b011a542\qrc_qml.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\software\sorting\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\software\sorting\qml.qrc" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{FE266B58-7748-378A-8744-938CC1169BC9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{1B386616-7733-3A2A-831B-F9BF697757DE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{EA787AE3-317C-3EE0-9503-2046540104E0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
