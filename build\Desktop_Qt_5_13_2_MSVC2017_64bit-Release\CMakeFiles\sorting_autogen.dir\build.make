# CMAKE generated file: DO NOT EDIT!
# Generated by "NMake Makefiles JOM" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

!IF "$(OS)" == "Windows_NT"
NULL=
!ELSE
NULL=nul
!ENDIF
SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\software\sorting

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release

# Utility rule file for sorting_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles\sorting_autogen.dir\compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles\sorting_autogen.dir\progress.make

CMakeFiles\sorting_autogen:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target sorting"
	echo >nul && "C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen.dir/AutogenInfo.json Release

sorting_autogen: CMakeFiles\sorting_autogen
sorting_autogen: CMakeFiles\sorting_autogen.dir\build.make
.PHONY : sorting_autogen

# Rule to build all files generated by this target.
CMakeFiles\sorting_autogen.dir\build: sorting_autogen
.PHONY : CMakeFiles\sorting_autogen.dir\build

CMakeFiles\sorting_autogen.dir\clean:
	$(CMAKE_COMMAND) -P CMakeFiles\sorting_autogen.dir\cmake_clean.cmake
.PHONY : CMakeFiles\sorting_autogen.dir\clean

CMakeFiles\sorting_autogen.dir\depend:
	$(CMAKE_COMMAND) -E cmake_depends "NMake Makefiles JOM" E:\software\sorting E:\software\sorting E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_64bit-Release\CMakeFiles\sorting_autogen.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles\sorting_autogen.dir\depend

