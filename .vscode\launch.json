{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "relwithdbginfo server",
      "type": "cppvsdbg",
      "request": "launch",
      "program": "sorting.exe",
      "args": [],
      "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\da914a0d82a514ce308bb9bb0c6e0ad6\\tonka3000.qtvsctools\\qt.natvis.xml",
      "stopAtEntry": false,
      "console": "internalConsole",
      "cwd": "${workspaceFolder}/build-RelWithDebInfo/bin",
      "environment": [
        {
          "name": "GLOG_v",
          "value": "12"
        },
        {
          "name": "GLOG_minloglevel",
          "value": "0"
        },
        {
          "name": "PortData",
          "value": "true"
        },
        {
          "name": "DEBUG_DB",
          "value": "true"
        },
        {
          "name": "Path",
          "value": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin;${env:PATH}"
        }
      ]
    }
  ]
}
