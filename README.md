# 质量检测与分拣系统

## 功能概述

这是一个基于Qt/QML的质量检测与分拣系统，具有以下主要功能：

### 1. 数据管理功能
- **导入数据**: 从CSV文件导入检测记录
- **导出数据**: 将检测记录导出为CSV文件
- **清空记录**: 清除所有检测记录
- **手动添加**: 手动添加单条检测记录

### 2. 测试功能
- **生成测试数据**: 自动生成10条测试记录
- **运行测试**: 执行系统功能测试

### 3. 实时显示
- **消息记录**: 显示所有操作的时间戳消息
- **检测记录表**: 显示所有检测记录的详细信息

## 数据格式

### CSV文件格式
```csv
时间戳,条码信息,检测结果,缺陷信息
2024-01-15 09:30:15,BC001,合格,无
2024-01-15 09:31:20,BC002,不合格,尺寸超差
```

### 检测记录字段说明
- **时间戳**: 格式为 "yyyy-MM-dd hh:mm:ss"
- **条码信息**: 产品条码或编号
- **检测结果**: "合格" 或 "不合格"
- **缺陷信息**: 具体的缺陷描述，合格时填写"无"

## 使用说明

### 1. 导入数据
1. 点击"导入数据"按钮
2. 在文件对话框中选择CSV文件
3. 系统会自动解析并导入数据
4. 导入结果会在消息记录中显示

### 2. 导出数据
1. 点击"数据导出"按钮
2. 在保存对话框中选择导出路径和文件名
3. 系统会将当前所有记录导出为CSV格式
4. 导出结果会在消息记录中显示

### 3. 手动添加记录
1. 在底部输入框中填写条码信息
2. 选择检测结果（合格/不合格）
3. 填写缺陷信息（合格时可填"无"）
4. 点击"添加"按钮

### 4. 测试功能
1. **生成测试数据**: 点击此按钮会清空现有数据并生成10条测试记录
2. **运行测试**: 执行系统功能测试，验证各项功能是否正常

## 界面说明

### 左侧面板
- **消息记录区**: 显示所有操作的时间戳消息
- **控制按钮区**: 包含所有功能按钮

### 右侧面板
- **检测记录表**: 以表格形式显示所有检测记录
- **手动添加区**: 用于手动添加新的检测记录

## 技术特性

### 后端功能 (C++)
- `SortingManager`类提供所有核心功能
- 支持CSV文件的读写操作
- 实时数据管理和信号通信
- 完整的测试框架

### 前端界面 (QML)
- 响应式布局设计
- 实时数据绑定
- 用户友好的操作界面
- 颜色编码的检测结果显示

### 数据特性
- 自动时间戳生成
- UTF-8编码支持
- 数据验证和错误处理
- 内存中的数据管理

## 编译和运行

### 编译
```bash
cmake -B build-Debug -S .
cmake --build build-Debug
```

### 运行
```bash
.\build-Debug\sorting.exe
```

## 测试文件

项目包含一个测试CSV文件 `test_data.csv`，可用于测试导入功能。

## 注意事项

1. CSV文件必须使用UTF-8编码
2. 时间戳格式必须为 "yyyy-MM-dd hh:mm:ss"
3. 检测结果只能是"合格"或"不合格"
4. 导入时会跳过CSV文件的标题行
5. 所有操作都会在消息记录中留下时间戳记录
