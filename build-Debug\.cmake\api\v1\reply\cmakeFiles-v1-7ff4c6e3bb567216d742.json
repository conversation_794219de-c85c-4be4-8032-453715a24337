{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build-Debug/CMakeFiles/3.29.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build-Debug/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "build-Debug/CMakeFiles/3.29.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake"}, {"path": "qml.qrc"}], "kind": "cmakeFiles", "paths": {"build": "E:/software/sorting/build-Debug", "source": "E:/software/sorting"}, "version": {"major": 1, "minor": 0}}