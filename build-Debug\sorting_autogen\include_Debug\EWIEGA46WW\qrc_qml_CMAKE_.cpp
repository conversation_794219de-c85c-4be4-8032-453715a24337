/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.13.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // E:/software/sorting/main.qml
  0x0,0x0,0xa,0xf9,
  0x0,
  0x0,0x5d,0x8,0x78,0x9c,0xed,0x5c,0x5b,0x6f,0xdb,0x46,0x16,0x7e,0xf7,0xaf,0x98,
  0x65,0xb0,0xa8,0x2,0x7b,0x55,0x5d,0x6c,0x47,0x56,0xd6,0xbb,0xb0,0x65,0x9,0x9,
  0x60,0x3,0x4d,0x6a,0x34,0xf,0x45,0x1f,0x68,0x72,0x24,0x11,0xa1,0x48,0x81,0xa4,
  0x6c,0xa7,0x85,0x81,0xe6,0xa1,0x8d,0xdb,0xb4,0xdd,0x0,0x8b,0x34,0x58,0x60,0xdd,
  0xc6,0x2d,0x16,0xdd,0xc5,0x2,0x4d,0xb0,0x6d,0x93,0x20,0xf6,0x22,0x7f,0x26,0x92,
  0x9d,0xa7,0xfc,0x85,0xce,0x70,0x66,0x28,0x8a,0xb7,0x19,0x4a,0x8a,0x63,0x27,0xfa,
  0xfc,0x60,0x93,0x73,0x66,0x38,0x33,0xe7,0xcc,0x77,0xce,0xdc,0xfc,0xf2,0xe0,0x99,
  0xd6,0x6a,0x9b,0x96,0x3,0xae,0x38,0x57,0x3a,0x9a,0x72,0x1d,0x14,0xb2,0xf9,0xc2,
  0xd4,0xe0,0xbb,0xec,0x35,0xcd,0x50,0xcd,0xad,0xc8,0xa4,0x8a,0x69,0x38,0x96,0xa9,
  0xdb,0x91,0x89,0xab,0xf2,0xd,0xb3,0xe3,0xd8,0x20,0x8f,0xd3,0xa6,0x68,0x29,0x9f,
  0x4c,0x1,0x4,0x4d,0x2d,0x83,0x96,0xac,0x19,0xe4,0xa5,0xfb,0x6a,0x53,0xb3,0xb5,
  0xd,0x1d,0x96,0x81,0x63,0x75,0xa0,0xfb,0x66,0x4b,0x53,0x9d,0x66,0x19,0xe4,0xb,
  0xb9,0x9c,0xfb,0xdc,0x84,0x5a,0xa3,0xe9,0x94,0x41,0x89,0x3e,0x3b,0x9a,0x83,0xe5,
  0xa5,0xe3,0x5f,0xff,0xfd,0xe2,0xd6,0xdf,0x7a,0x3f,0x7e,0xda,0xfb,0xed,0xf6,0xf3,
  0x27,0xdf,0x74,0x77,0x3f,0xef,0xdd,0xfe,0xf1,0xe8,0x97,0x83,0xa3,0x83,0xef,0xa5,
  0x29,0x57,0xb2,0x6d,0x99,0x6d,0x68,0x39,0x37,0x80,0x62,0xea,0xa6,0x85,0x1e,0xb5,
  0x96,0x6c,0xdd,0xa8,0xe0,0x7,0x94,0xff,0x5c,0x21,0xbf,0x30,0x5f,0x2b,0x4a,0x51,
  0xa2,0xb2,0xa2,0x40,0xc3,0xf1,0x24,0x6b,0xb5,0x5,0xf4,0xf5,0x48,0xc9,0xd,0x59,
  0xb9,0xde,0xb0,0xcc,0x8e,0xa1,0xf6,0xa5,0x97,0xf0,0x4f,0xa4,0xb4,0xdd,0xb1,0xea,
  0xb2,0x2,0x7d,0x5,0x63,0xd0,0xea,0xbe,0xfb,0x2e,0x40,0x1d,0x6b,0x40,0xc5,0x1,
  0x8e,0x9,0x2a,0xd3,0xd3,0xc0,0xd6,0x1a,0x86,0xac,0xdb,0x6e,0x2a,0x4d,0xd2,0x4c,
  0xc3,0xa6,0xbd,0xe9,0x76,0x86,0x6c,0x35,0x20,0xea,0x1c,0x1b,0x69,0x40,0x33,0x1a,
  0x6b,0xb2,0x21,0x37,0xa0,0xe5,0x25,0x9b,0xc6,0x1a,0xb4,0x6d,0xf4,0x6a,0x49,0x55,
  0x21,0xea,0xfc,0x7e,0x46,0x8c,0x16,0x49,0x5b,0x33,0x55,0xa8,0x67,0x35,0xc3,0x46,
  0x15,0xcd,0xe4,0x66,0x2,0x42,0x18,0x12,0x95,0x94,0xca,0x2c,0xcf,0x4c,0x58,0xc6,
  0xd1,0x5a,0x58,0x0,0xff,0xb2,0x1d,0xb9,0xd5,0x1e,0x90,0xd8,0x39,0x3f,0xf0,0x88,
  0x9a,0xba,0xd4,0x41,0x8d,0xb4,0x15,0x64,0x46,0x3a,0x6e,0xae,0x63,0xe,0x66,0xd0,
  0xea,0x20,0x43,0xbf,0xb5,0xaa,0xd9,0x4e,0x56,0x41,0x5d,0xec,0x80,0xbf,0x80,0x7c,
  0x2e,0x77,0x3e,0xa2,0x86,0x3,0x4d,0xb1,0x60,0xcb,0xdc,0x84,0x19,0x24,0x3a,0x33,
  0x98,0x40,0xa,0xf9,0x93,0x5b,0xc8,0x60,0xf5,0xa6,0xc2,0x7f,0x99,0xc6,0x8a,0xec,
  0xc8,0x97,0x5d,0xe3,0xe,0xf7,0x9d,0x5,0x15,0xd3,0x52,0x69,0xb1,0x3a,0x94,0xad,
  0xcc,0x60,0x89,0x9b,0xb2,0x45,0x65,0x6c,0xb0,0x18,0x50,0x4f,0x16,0xa9,0x6c,0x5,
  0x3a,0x44,0x9b,0x57,0x89,0x50,0x20,0x7b,0x1d,0x19,0x4b,0x6,0x97,0xa1,0xa1,0xdc,
  0xb9,0x8b,0xe8,0xd7,0x9f,0x59,0x71,0x59,0x1d,0x1a,0xd,0xa7,0x89,0xde,0x4d,0x4f,
  0x47,0x75,0x85,0xbf,0x66,0x72,0xbb,0xd,0xd,0x35,0x43,0x73,0x7e,0xa8,0x7d,0x14,
  0xd7,0x6c,0xc,0xa4,0x35,0xa7,0x63,0x2f,0xcb,0x56,0xd6,0x6e,0x9a,0x5b,0xd4,0x70,
  0x32,0x52,0xf7,0xf1,0xff,0xba,0xf,0xe,0xbb,0x9f,0xfd,0xb,0x48,0x60,0x3a,0x50,
  0x9,0xf4,0x42,0x2,0xbd,0xbd,0xfd,0xe3,0x9f,0x1f,0x76,0xff,0x7f,0x57,0x9a,0x1,
  0xc5,0x9c,0xbf,0x6b,0xfd,0x9d,0x49,0xda,0x19,0x69,0x87,0xfe,0x1a,0xf7,0xcd,0x90,
  0xbc,0x8d,0x2e,0xc,0x6b,0xa6,0xba,0x1d,0xad,0x99,0x98,0x66,0xf4,0xee,0x3e,0xec,
  0x7d,0xfd,0x33,0x6e,0xc9,0xad,0xa7,0xbd,0xdd,0x3b,0xdd,0x2f,0xbf,0x8f,0xa9,0xee,
  0xe,0x19,0x8d,0xa8,0xbe,0x8e,0x6c,0x34,0x74,0xe8,0x2b,0x5e,0x36,0x94,0xa6,0x69,
  0xd9,0xd9,0xba,0xa6,0xeb,0x65,0xd0,0x96,0x2d,0x44,0x12,0x5e,0xa2,0x42,0x6,0x75,
  0x80,0x10,0xa6,0xbc,0xf4,0xab,0xe6,0x16,0xa1,0xc6,0x40,0x7d,0x13,0xb,0xf5,0xb,
  0x20,0xf2,0x6a,0xa0,0xee,0x41,0xd4,0x98,0x1b,0x6c,0x70,0x5b,0x56,0x90,0x71,0xb9,
  0xef,0x83,0xa3,0x6c,0x15,0xd6,0x1d,0xf0,0x9e,0x6c,0x40,0x1d,0x99,0x3d,0xed,0xb,
  0xb0,0x6a,0x36,0x6,0xe4,0xa2,0x9a,0xca,0x40,0xaa,0xec,0x56,0xee,0x12,0x25,0x62,
  0x8f,0xa9,0x23,0xe4,0xda,0x16,0xac,0x43,0xcb,0x82,0xea,0x35,0xc2,0xe2,0xb3,0xb9,
  0x5c,0x48,0x94,0x76,0x94,0x9f,0xb,0xc3,0x46,0x2c,0xab,0x5a,0x7,0x35,0xb5,0x14,
  0x4a,0xd9,0x40,0x46,0x81,0x86,0x90,0xc2,0x28,0xb4,0x9a,0xc3,0x3f,0x52,0x9c,0x1c,
  0xf3,0x26,0x53,0x21,0x1,0xf4,0xe1,0x4e,0xcb,0x88,0x54,0x9,0x3,0x57,0x35,0x41,
  0x41,0x4f,0x45,0xe1,0x7a,0x63,0x78,0x9a,0xca,0x85,0xab,0x83,0x91,0xa4,0x8,0x6,
  0x9f,0x42,0x68,0x1f,0x47,0xea,0x83,0x81,0x79,0xcf,0xd9,0xb0,0x1e,0x18,0x68,0x57,
  0xfa,0xdd,0x63,0xac,0x2c,0xd3,0xcb,0x7c,0x74,0x3,0x30,0xd6,0xe1,0x76,0x5c,0x7f,
  0x32,0xb0,0xee,0xc2,0x4e,0x16,0x5a,0x97,0x8d,0xc4,0xbe,0x65,0x70,0x50,0xb9,0x48,
  0xe1,0xbd,0x47,0xbb,0xbd,0x9b,0xf,0x28,0xe1,0x24,0x66,0x60,0x26,0xb2,0xd5,0xd4,
  0x1c,0x98,0x2c,0x5a,0x47,0x1,0x4d,0x76,0xc3,0xd4,0x55,0x4e,0x6f,0x7a,0xb2,0x6d,
  0x6d,0x1b,0xea,0xef,0x6b,0x1f,0xa3,0x40,0x24,0x3f,0x1f,0x2b,0xbe,0x13,0x99,0xb2,
  0x13,0xdd,0x77,0xd8,0xcd,0x7d,0xa0,0xc1,0xad,0x84,0xbe,0x73,0xe3,0xa7,0xbe,0x53,
  0x1c,0x97,0x8d,0x88,0x8e,0xf1,0x80,0x3c,0x72,0xd9,0x6b,0xae,0xbd,0xc7,0x99,0x3b,
  0x86,0xa2,0x6b,0x6d,0x4e,0x71,0xde,0xa8,0x98,0x8d,0x15,0xd9,0x44,0x3e,0x41,0x53,
  0x64,0x9d,0x7c,0x79,0x45,0xb3,0x88,0xef,0x2c,0x7b,0x9d,0x96,0x5d,0x36,0x1d,0xc7,
  0x6c,0xad,0x9b,0xeb,0x28,0x8e,0x88,0x2d,0xa6,0x85,0x5d,0xc,0xc9,0xe4,0x7a,0x1b,
  0x8e,0x99,0xfa,0xba,0xdb,0x15,0x4f,0x14,0xae,0x98,0x28,0x52,0x30,0x90,0x11,0x67,
  0x4d,0x3,0xff,0xad,0xc3,0x8,0xdf,0x14,0x5,0xea,0xa3,0xf9,0x82,0x18,0xbe,0x48,
  0x4c,0x22,0xb1,0x6e,0x77,0x77,0xaf,0xfb,0xd3,0xed,0xee,0x57,0xdf,0x32,0xa7,0x26,
  0x56,0xe,0x8d,0xd6,0xae,0x20,0xb5,0x9b,0x56,0x4b,0x76,0xd6,0xd1,0x73,0xc6,0x40,
  0xd6,0x87,0x5c,0x2b,0xcc,0x9c,0x9f,0x1,0x52,0xb3,0x59,0x6e,0xb5,0xca,0xb6,0x2d,
  0x9d,0xe7,0x96,0xb8,0x93,0x2c,0x12,0x3d,0xc,0x48,0x4a,0x6c,0x12,0xea,0x6f,0xd8,
  0x40,0x75,0x29,0xb,0xb1,0x22,0x6,0xa5,0x7a,0x7f,0xd4,0xe8,0xbe,0x4a,0xcc,0xc4,
  0xf8,0x71,0x4d,0x76,0x9a,0x88,0xbe,0xb7,0x33,0xb3,0xfd,0xa0,0x11,0x33,0x19,0x72,
  0x34,0x88,0xa3,0xc,0x87,0xc,0xc,0x14,0xef,0xe4,0xe7,0x93,0x1b,0xeb,0x39,0xa6,
  0xda,0x1c,0xfe,0x49,0xe6,0x1d,0x51,0x67,0xe6,0x87,0x0,0x5,0x63,0x88,0xf6,0x1a,
  0x6,0xed,0xb9,0xf8,0xf1,0xc7,0xc0,0x7a,0x8b,0xd0,0x75,0x96,0x3c,0x72,0x73,0xa5,
  0x71,0x30,0xc,0xac,0x95,0x5,0x8e,0x65,0x25,0x26,0xb,0xb8,0x22,0xc,0xdf,0x38,
  0xc7,0x39,0xf8,0x23,0x96,0xba,0x2f,0x1d,0x5,0x57,0x5e,0x5f,0xe0,0x7,0xe1,0x9c,
  0x16,0x65,0x59,0x34,0xe8,0x5c,0x23,0x4b,0x95,0x19,0xf1,0xae,0xf7,0xd5,0xe0,0xb4,
  0x29,0x29,0xdf,0x86,0x4b,0x91,0x5e,0x56,0xf2,0x98,0xaa,0xb1,0x8c,0xee,0xf3,0xc9,
  0x5a,0x9,0xb5,0x94,0xef,0x26,0x22,0xda,0x98,0x3e,0x13,0x69,0x91,0x78,0x3e,0x12,
  0x51,0x50,0xcd,0x8b,0x5a,0xb1,0x74,0xae,0xe8,0x22,0x79,0x90,0x62,0x6c,0x59,0x72,
  0x1b,0xfb,0x8d,0xb2,0x6b,0x87,0xd9,0x6b,0x68,0xac,0x5f,0x43,0xaf,0xb8,0xf9,0x42,
  0x1,0x6,0xbf,0xb7,0x99,0x7f,0x5c,0xd2,0xb5,0x86,0xd1,0x42,0xda,0xa5,0xdf,0x74,
  0x9f,0x3f,0xa8,0xb8,0x61,0xd6,0x9,0xd,0x24,0x66,0xd3,0x69,0xc7,0x2,0xb5,0x4a,
  0x4b,0x88,0x51,0x46,0x1d,0xa,0x9c,0x38,0xdd,0xf,0x62,0x24,0xb8,0x55,0xe2,0x16,
  0x32,0xef,0x82,0x6f,0x21,0x21,0x4d,0xc7,0x7,0xe9,0x3,0x59,0xea,0x72,0x4b,0xd3,
  0x6f,0xa0,0x2f,0x55,0x4c,0xc3,0x36,0x75,0xd9,0x46,0x2e,0xcb,0x34,0x4c,0x1c,0x48,
  0x71,0x2,0xdd,0x24,0x57,0x1c,0xfd,0x36,0xf2,0x35,0x59,0xaa,0xc2,0x6b,0x80,0x60,
  0xb9,0x83,0x6,0x9d,0x61,0x83,0x25,0xb,0xca,0x27,0x3d,0xa9,0xc9,0xcf,0x73,0x67,
  0x35,0xc8,0xf,0x97,0x6a,0xb,0x6c,0x39,0x2e,0xa,0x7d,0x7f,0x1a,0x27,0x91,0xd6,
  0x4b,0x73,0xa7,0x9e,0xc,0x2,0x53,0x50,0x6,0xe1,0xa9,0x68,0x30,0x43,0x7f,0xd5,
  0x20,0x99,0x46,0xbc,0x28,0xbc,0x34,0x16,0x2a,0x60,0xb3,0xb5,0x6f,0x7e,0xea,0xee,
  0x3e,0x7a,0xb1,0xf7,0x43,0x6f,0xef,0x99,0xe0,0x78,0x10,0x9c,0x86,0x79,0xf2,0xfe,
  0xf1,0xc3,0x8f,0x60,0x52,0xb1,0x38,0x87,0x15,0xd1,0x28,0xa8,0x69,0x96,0xed,0x0,
  0xcb,0xdc,0x2,0x1b,0x64,0x1c,0x24,0xc7,0x64,0x31,0x4b,0x40,0x51,0x48,0x39,0x24,
  0x18,0x44,0xd5,0x88,0x41,0x86,0xae,0x40,0x5d,0x30,0xa8,0x42,0xc9,0x52,0x20,0x59,
  0x4c,0xe3,0x2b,0xd4,0xd7,0x90,0xe0,0xca,0xd0,0x2,0x9f,0xeb,0xa2,0x72,0xb3,0x9,
  0x6a,0x91,0xef,0x14,0x31,0xd0,0x5c,0x4c,0xd7,0x94,0xeb,0x62,0x33,0x31,0x6,0xbc,
  0xee,0x8a,0xfa,0x1d,0xbe,0x87,0xe6,0x4,0xe1,0xc5,0x5b,0x1b,0x4d,0x4c,0x14,0x87,
  0xac,0xb,0xd7,0x90,0x54,0x86,0x3f,0x3f,0x62,0xc0,0x8b,0xda,0x5e,0xc1,0x7f,0x58,
  0x5c,0x4,0x92,0x14,0xb5,0x82,0x9b,0x84,0x40,0x65,0xc8,0xde,0xb,0x5e,0xe,0xad,
  0x59,0x66,0xcb,0xad,0xe,0xfb,0x80,0x78,0xb5,0xe2,0x9d,0xc2,0xa0,0x94,0x90,0x58,
  0x7f,0xfd,0x53,0x7c,0xe6,0xe6,0x7,0x9b,0x2c,0x10,0xa7,0x8e,0xf4,0x6e,0xdb,0x50,
  0x5,0x7f,0x45,0x23,0x36,0xbf,0x70,0x61,0x7e,0xa5,0x20,0x81,0x94,0x33,0x9,0x6,
  0xc6,0xf3,0x7c,0x8e,0xc0,0x10,0x6c,0x2d,0x9d,0x1d,0x5e,0x76,0x60,0xab,0x2c,0x4a,
  0x8d,0xc,0x64,0x44,0xb1,0xe8,0x45,0x24,0x64,0xea,0x7f,0x55,0x78,0x65,0xcb,0x8f,
  0x10,0x5d,0xe6,0x85,0xb3,0x22,0x57,0xa2,0x7d,0x8c,0xb2,0xc7,0x84,0x98,0x97,0x4,
  0x42,0x4c,0x3f,0x46,0x8e,0x57,0x19,0xf8,0xa6,0x2b,0xa0,0xc8,0x61,0x79,0xf0,0xd6,
  0xd3,0x9,0xf,0x92,0x5d,0x18,0x2c,0xf1,0x5a,0x79,0x10,0x6e,0x33,0x1e,0x5c,0x37,
  0x2b,0xf6,0xe6,0x9b,0x4b,0x82,0xd5,0xf9,0xb9,0x7c,0x2e,0x87,0x49,0xd0,0xb7,0x47,
  0x3d,0xe1,0xc0,0x9,0x7,0xe,0x2f,0xc1,0x8f,0x72,0xdf,0x87,0x48,0xc7,0xea,0xdb,
  0x14,0xe6,0xf6,0x9e,0x7c,0x76,0xf4,0x9f,0xa7,0x22,0xbb,0x4c,0x81,0x86,0x9c,0x21,
  0x7a,0xf,0x50,0xa8,0x7b,0x8a,0x21,0xfa,0x34,0x42,0x12,0x78,0x7,0x21,0xe2,0xf0,
  0xda,0x99,0xb4,0x32,0x5f,0x28,0x15,0x4a,0x98,0x49,0xa5,0x73,0xb5,0xd9,0xd9,0x62,
  0x51,0x60,0xc1,0x86,0x61,0xc2,0xa3,0x6f,0x29,0x8f,0xbe,0x1a,0xb2,0xf9,0xed,0xf6,
  0xf1,0x83,0xbb,0x6f,0x76,0x2c,0x19,0x3a,0x1,0x65,0x40,0x4b,0x76,0xe0,0x3a,0xb4,
  0xdd,0xa8,0xed,0xac,0x90,0x46,0xb1,0x54,0xaa,0x16,0x2b,0x84,0x34,0x66,0x2b,0x4b,
  0xb5,0x39,0xce,0x66,0x9d,0x1f,0x13,0xd2,0x78,0x4b,0x49,0x23,0x39,0x95,0x17,0x7c,
  0xad,0x37,0x35,0xcb,0x1f,0x7b,0x25,0xca,0xb,0xd3,0xf,0xa5,0x9e,0xe3,0x67,0x77,
  0x8e,0xf7,0xbf,0x22,0x4,0xc4,0xd7,0x63,0xc,0xed,0xe4,0xb,0x7c,0xde,0x19,0x81,
  0x73,0x52,0xf3,0x4d,0x80,0x6b,0xac,0x8e,0x81,0x69,0x46,0x24,0xa8,0x11,0x18,0x74,
  0xa3,0x70,0x4b,0x3c,0xaf,0x5c,0x58,0xce,0xd7,0x96,0xa,0x84,0x57,0x16,0x2a,0x85,
  0xb,0xcb,0x82,0xbc,0x22,0xce,0x29,0x2,0x4d,0x1b,0x96,0x4b,0x86,0xe3,0x91,0x21,
  0x38,0x64,0x48,0xfe,0x18,0x23,0x77,0x8c,0x85,0x37,0x78,0x8c,0x90,0x2e,0x25,0xfc,
  0x76,0xf0,0xcd,0x4e,0xe8,0x50,0xe8,0x55,0xf7,0x28,0xb,0x3b,0x15,0xea,0x9d,0x44,
  0x6,0x34,0xf8,0x1f,0x10,0x1f,0xe3,0xd9,0x50,0xde,0x1c,0x6f,0x72,0x2e,0x74,0x72,
  0x2e,0x74,0x84,0x73,0xa1,0xee,0x45,0x90,0x37,0xf0,0x5c,0x28,0xe,0x1,0xe4,0xd,
  0xa4,0xfb,0x4b,0x50,0x56,0x63,0xd8,0xe5,0xb5,0x1a,0x8,0xff,0xa8,0xdb,0x2b,0xdb,
  0x40,0x17,0x5d,0x69,0x1a,0x7d,0xf7,0x3c,0xf9,0xa0,0x48,0xff,0x8,0xfe,0x58,0x36,
  0xcf,0xe3,0xe2,0xac,0x4,0x5d,0x30,0xb0,0xd1,0x70,0xef,0xd1,0x8b,0x7b,0xbf,0xf6,
  0x76,0x7f,0x39,0x81,0x4d,0x77,0x7e,0x4,0x37,0x26,0x7,0x3c,0x86,0x53,0x4e,0x49,
  0xa9,0xbe,0x41,0xe4,0x5d,0x42,0xbb,0x18,0xe7,0xe4,0x2e,0xf6,0xad,0x7f,0x25,0x87,
  0x7f,0x24,0x4e,0xe9,0x23,0xaa,0x3e,0xe2,0x1e,0x45,0x10,0x4c,0xf5,0x7b,0xfb,0x47,
  0xf7,0x6f,0x4e,0xf4,0xee,0xc3,0xd9,0xd5,0x7b,0x49,0x58,0xed,0x47,0x7,0x7f,0xef,
  0x7d,0xf7,0xcf,0x89,0xda,0x7d,0x38,0x3b,0x6a,0x4f,0xbb,0xfd,0xc1,0x74,0x7e,0xf8,
  0xf4,0xc5,0x3f,0x1e,0x3f,0x7f,0xb6,0xdf,0xbb,0xf9,0x60,0xa2,0x79,0x1f,0xc6,0x74,
  0xcc,0x51,0xf0,0x4a,0xe,0xd9,0x2,0x79,0x13,0x6f,0xe4,0x14,0xc6,0x7a,0x97,0xc6,
  0xb7,0x55,0x94,0xa0,0x9f,0xd8,0xa4,0xa1,0x6f,0x84,0xf4,0xf5,0x93,0xe2,0x42,0x48,
  0x31,0x3e,0x7c,0xc7,0xa0,0x74,0xa0,0x19,0x2a,0xdc,0x6,0x7f,0x4,0x5,0xb0,0xb8,
  0xb8,0x8,0x72,0xee,0x5a,0xe,0xbd,0xd4,0xd,0x44,0xe,0x9f,0x62,0x8c,0x72,0x9,
  0x64,0x96,0x73,0x9,0x24,0xc5,0x4e,0x6c,0xea,0x18,0xd9,0x9f,0x29,0xc5,0x81,0x6a,
  0xd1,0x58,0x19,0x23,0xc5,0x22,0xd4,0x8,0x31,0x33,0x46,0xff,0x98,0x77,0xf8,0xf2,
  0x7a,0x1c,0x86,0x38,0xbe,0xed,0x65,0x1b,0xfe,0x8,0x37,0xc3,0x69,0x5b,0xd5,0xc2,
  0xf0,0x6c,0x78,0xce,0x5,0xbf,0x21,0xfc,0xe5,0xf3,0x61,0xdd,0x34,0x1d,0x45,0x2,
  0x5f,0x18,0x83,0x91,0x9,0x44,0xe7,0x18,0xc4,0xc8,0x36,0x64,0x4b,0x41,0x1c,0xf8,
  0x2a,0x57,0x3c,0xd3,0xba,0x7a,0x8c,0x53,0x6d,0x4f,0xa2,0x77,0x6e,0x4e,0x83,0x3d,
  0xa5,0xdd,0x15,0x18,0x3e,0xf4,0xf7,0xe5,0x16,0xe,0x15,0xfc,0xa0,0x8d,0x53,0xfb,
  0xff,0x8b,0xc2,0xee,0xe8,0x8e,0xeb,0xca,0xa4,0xee,0x9d,0xdd,0xde,0xfd,0x43,0x89,
  0x1c,0x3a,0x2b,0xd5,0xe6,0xaa,0xec,0xa8,0x44,0xad,0xba,0x5c,0xad,0xa6,0xdb,0x9d,
  0x28,0x8a,0xed,0x65,0xa6,0xdc,0xbe,0x1c,0x6a,0xb5,0xd0,0xf,0x32,0x1c,0x3,0xad,
  0x17,0xce,0x3d,0xc2,0x66,0xa6,0x68,0xbf,0x17,0xaa,0x17,0x56,0x8a,0x74,0x57,0x88,
  0x1e,0x57,0x49,0x57,0xbd,0x94,0x14,0x30,0xea,0x66,0x26,0xc6,0x69,0xe4,0xeb,0xb4,
  0xd3,0x2b,0xc,0x66,0x1c,0x75,0x7c,0xd8,0xdf,0xa8,0x9b,0xaf,0x92,0xae,0x39,0xd4,
  0xbb,0x2a,0x72,0x3b,0x14,0x63,0x6c,0xbc,0xb,0x75,0xcd,0xbb,0xb7,0x58,0xc5,0x7f,
  0x5f,0x15,0xba,0x96,0x87,0x31,0xe6,0x10,0x60,0x7c,0x17,0xd6,0xd6,0x64,0xa3,0x23,
  0xeb,0x60,0x49,0x55,0xe9,0x46,0xd7,0x6b,0xb9,0xb2,0x96,0xc0,0xea,0xc2,0x37,0xd6,
  0x46,0x5d,0x4e,0x8f,0x93,0x13,0xd8,0xc4,0x79,0xb,0x6e,0xac,0x3d,0x3e,0xe8,0x7e,
  0x79,0xbf,0xf7,0xed,0x43,0xd1,0xc3,0x9f,0x27,0xb0,0xac,0x32,0xce,0x4b,0x6b,0xa7,
  0xec,0x70,0x2e,0x56,0x4e,0x4d,0x83,0xba,0x2a,0xc8,0xe8,0x78,0x1d,0x81,0x6,0xd0,
  0x97,0x8d,0x76,0x47,0x8c,0x93,0x46,0xc,0xdb,0xdb,0x3a,0x9a,0x94,0x35,0x91,0x86,
  0xa1,0xb5,0x9e,0x6e,0x8d,0x1d,0x23,0xc2,0x27,0x8,0xe5,0x1b,0xd3,0x21,0xb6,0xfe,
  0x3f,0x99,0x13,0xcd,0x18,0xa4,0x17,0xba,0xe0,0x99,0x36,0x3f,0x97,0x6e,0x82,0x48,
  0x7b,0x3e,0x8e,0x2f,0xc1,0x15,0xa9,0x98,0xad,0xd,0x73,0xd9,0xdc,0x4e,0x61,0x7c,
  0x96,0x1b,0xab,0xb9,0x19,0x4f,0xc2,0xf6,0xe8,0x32,0xdb,0x87,0x2c,0x2e,0x9c,0x1,
  0xd2,0xf3,0x27,0x5f,0xd3,0x87,0x8f,0x26,0x6,0x98,0x90,0xff,0x2c,0x18,0xe0,0x30,
  0xf4,0xc7,0x62,0xd2,0x94,0xec,0x37,0x4c,0x10,0x1c,0x66,0x3e,0xff,0x96,0xc3,0xcb,
  0xc3,0x5d,0x62,0x88,0xbd,0x7b,0x8f,0xba,0xfb,0xff,0x7d,0xa7,0x77,0xef,0xfe,0x3b,
  0x2f,0xf,0xbf,0x98,0xd0,0x62,0x52,0xfe,0xb3,0x60,0x95,0x43,0x1d,0x62,0x77,0xe3,
  0xa6,0x91,0x8e,0xaf,0x27,0xfc,0x37,0x87,0xa4,0xdc,0x27,0x71,0x7c,0x1d,0xdf,0x58,
  0xf4,0x87,0x1d,0xee,0x31,0xc7,0x21,0xaf,0x2e,0xe2,0x6b,0x95,0x64,0xc,0x83,0x45,
  0xff,0x60,0x26,0x65,0xba,0x8b,0x0,0xee,0xf4,0x1f,0xd,0x27,0x3c,0xf5,0xf,0x4a,
  0xa4,0xfa,0x56,0xe0,0x28,0xac,0xac,0xaa,0x81,0x7f,0x3c,0x9a,0x49,0x55,0x1c,0x46,
  0xa8,0x1b,0xc4,0xfe,0xe3,0x98,0x1f,0x3e,0x1f,0x9a,0x55,0x3a,0x16,0x9e,0x19,0xac,
  0xf,0x55,0x10,0xe9,0x9b,0x54,0xd9,0xc4,0x6f,0x36,0x61,0x84,0x75,0x8e,0xb5,0x93,
  0xaa,0x88,0xb0,0x86,0xd3,0x94,0x70,0x46,0xae,0xa5,0x4e,0xee,0x45,0xd0,0xaf,0x4e,
  0xee,0x45,0x10,0x8c,0x7e,0x2f,0x22,0x5d,0xa,0xff,0x14,0xf4,0xe0,0x5f,0x3b,0x53,
  0x3b,0x53,0xbf,0x3,0xb,0xca,0xf5,0x49,
  
};

static const unsigned char qt_resource_name[] = {
  // main.qml
  0x0,0x8,
  0x8,0x1,0x5a,0x5c,
  0x0,0x6d,
  0x0,0x61,0x0,0x69,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/main.qml
  0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0xa2,0x68,0x9e,0x30,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)(); }
   } dummy;
}
