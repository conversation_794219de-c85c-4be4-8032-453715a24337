/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.13.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // E:/software/sorting/main.qml
  0x0,0x0,0xa,0x86,
  0x0,
  0x0,0x5d,0x47,0x78,0x9c,0xed,0x5c,0xcd,0x72,0x1b,0xb9,0x11,0xbe,0xeb,0x29,0x90,
  0x71,0x25,0x45,0x97,0x15,0x2e,0x7f,0x64,0x59,0xa2,0xa3,0xa4,0x24,0x4a,0x2c,0xbb,
  0xca,0xda,0xb2,0x65,0xd5,0xfa,0xb0,0xb5,0x7,0x88,0x3,0x92,0x28,0xf,0x7,0xac,
  0x99,0xa1,0x7e,0x9c,0xd5,0x3d,0xc7,0x3c,0x42,0xde,0x63,0xb7,0x2a,0x4f,0x93,0x5c,
  0xf3,0xa,0xe9,0x1e,0x0,0xc3,0xe1,0xfc,0x1,0x43,0xd2,0xb6,0x2c,0xf3,0xd3,0x41,
  0xe4,0xa0,0x81,0x1,0xd0,0x8d,0xaf,0x1b,0x7f,0xfc,0xdf,0x6f,0xff,0xe6,0xd3,0x99,
  0x8,0x22,0xf2,0x2e,0x7a,0x37,0xe7,0xc3,0x8f,0xa4,0xd3,0x6c,0x77,0x76,0x96,0x9f,
  0x35,0x3f,0x70,0xdf,0x15,0x37,0x85,0x49,0x7d,0xe1,0x47,0x81,0xf0,0xc2,0xc2,0xc4,
  0x37,0xf4,0x4e,0xcc,0xa3,0x90,0xb4,0x31,0x6d,0x47,0x95,0xf2,0xf7,0x1d,0x2,0xe0,
  0x6e,0x8f,0x4c,0x29,0xf7,0xe5,0xc3,0xf8,0xd1,0x35,0xf,0xf9,0x95,0xc7,0x7a,0x24,
  0xa,0xe6,0x2c,0x7e,0x72,0xc3,0xdd,0x68,0xd2,0x23,0xed,0x4e,0xab,0x15,0x7f,0x9f,
  0x30,0x3e,0x9e,0x44,0x3d,0x72,0xa0,0xbe,0x47,0x3c,0x42,0x79,0xe7,0xdd,0x9c,0x7a,
  0x3c,0xba,0x23,0xa7,0x2c,0x62,0xc3,0x88,0xb,0x9f,0xfc,0x89,0xbc,0x87,0x8a,0x70,
  0x7f,0x4c,0xde,0xdf,0x85,0x11,0x9b,0x3a,0x3b,0x71,0x86,0x59,0x20,0x66,0x2c,0x0,
  0xc9,0xa1,0xf0,0x44,0x0,0x5f,0xf9,0x94,0x6,0x77,0x7d,0xfc,0x2,0xc5,0x3c,0xe9,
  0xb4,0xf,0xf7,0x7,0x5d,0xa7,0x48,0x94,0xe,0x87,0xcc,0x8f,0x12,0xc9,0xc1,0xe0,
  0x10,0x2a,0x51,0x28,0x79,0x45,0x87,0x1f,0xc7,0x81,0x98,0xfb,0xee,0x42,0xfa,0x18,
  0xff,0xa,0xa5,0xc3,0x79,0x30,0xa2,0x43,0x96,0x2a,0x18,0xa1,0xaa,0xfb,0xc3,0xf,
  0x4,0xfa,0xd7,0x87,0x36,0x91,0x48,0x90,0xfe,0xb3,0x67,0x24,0xe4,0x63,0x9f,0x7a,
  0x61,0x9c,0xaa,0x92,0xa0,0xb9,0xa1,0xea,0xd4,0xb8,0x4f,0x68,0x30,0x66,0xd0,0x47,
  0xa1,0x6c,0xff,0x39,0xf5,0xe9,0x98,0x5,0x49,0xb2,0xf0,0xcf,0x59,0x18,0xc2,0xa3,
  0x63,0xd7,0x65,0xa0,0x83,0x45,0x46,0xc4,0x54,0xa6,0x9d,0xb,0x97,0x79,0x4d,0xee,
  0x87,0x50,0xd1,0x46,0x6b,0x37,0x23,0x84,0x70,0x94,0xa4,0xd3,0xd3,0x79,0x76,0xf3,
  0x32,0x11,0x9f,0xa2,0x0,0xfe,0xb,0x23,0x3a,0x9d,0x2d,0x49,0xdc,0x3f,0x5d,0xfa,
  0xa,0x4d,0x3d,0x9e,0x43,0x23,0xc3,0x21,0x58,0x93,0x87,0xcd,0x8d,0xc4,0x72,0x6,
  0x3e,0x22,0xd,0xf5,0xae,0x37,0x3c,0x8c,0x9a,0x43,0xe8,0xe2,0x88,0xfc,0x95,0xb4,
  0x5b,0xad,0xa7,0x5,0x35,0x5c,0x6a,0x4a,0xc0,0xa6,0xe2,0x9a,0x35,0x40,0x74,0x77,
  0x39,0x41,0x16,0xf2,0xe7,0xb8,0x90,0xe5,0xea,0xed,0xe4,0x3f,0x9,0xff,0x94,0x46,
  0xf4,0x75,0x6c,0xe3,0xf9,0xbe,0xb,0xd8,0x50,0x4,0xae,0x2a,0xd6,0x63,0x34,0x68,
  0x2c,0x97,0x78,0x4d,0x3,0x25,0x13,0x92,0xa3,0x8c,0x7a,0x9a,0xa0,0xb2,0xc4,0x78,
  0x2f,0xa4,0x50,0x26,0xfb,0x8,0x8c,0xa5,0x81,0x65,0x70,0xc8,0xdd,0x7a,0x9,0xff,
  0xfe,0xa2,0x8b,0x6b,0x7a,0xcc,0x1f,0x47,0x13,0x78,0xf6,0xec,0x59,0x51,0x57,0xa4,
  0x6b,0x46,0x67,0x33,0xe6,0xbb,0xd,0x95,0xf3,0x67,0xfe,0x4b,0x59,0xb3,0x11,0xa0,
  0xb5,0x68,0x1e,0x9e,0xd0,0xa0,0x19,0x4e,0xc4,0x8d,0x32,0x9c,0x86,0xa3,0x7b,0x80,
  0x38,0xe4,0x59,0xa6,0xe,0xf0,0xc0,0xd1,0x8f,0x9c,0x5d,0xd2,0x6d,0xa5,0xfb,0x35,
  0xdd,0x93,0xb2,0x91,0x85,0x46,0x98,0xae,0xee,0xc2,0x6,0xe5,0xd3,0xe2,0xc2,0x50,
  0x2d,0x67,0xb7,0xc5,0x6a,0x29,0x69,0x3,0x66,0x21,0x4c,0xe5,0x81,0x51,0x8,0xc3,
  0x3b,0xc,0x47,0x73,0xcf,0xbb,0x2b,0xa9,0xf6,0xbd,0x1c,0x92,0x50,0xef,0x88,0xfa,
  0x63,0x8f,0xa5,0x5e,0x43,0xfd,0xe1,0x44,0x4,0x61,0x73,0xc4,0x3d,0xaf,0x47,0x66,
  0x34,0x0,0xa6,0x48,0x12,0x87,0x72,0x64,0x67,0x58,0x61,0x27,0x49,0xbf,0x10,0x37,
  0x92,0x26,0x33,0xf5,0xae,0x2c,0x34,0x2d,0x0,0xc,0x36,0x86,0x6e,0x2,0x9a,0x6c,
  0x2d,0x37,0x7c,0x46,0x87,0x60,0x61,0xf1,0xf3,0xec,0x50,0x7b,0xc3,0x46,0x11,0x79,
  0x4b,0x7d,0xe6,0x81,0xed,0xab,0x3e,0x21,0x6f,0xc4,0x78,0x49,0xae,0xa8,0xa9,0x1a,
  0xb2,0xca,0x71,0xe5,0x5e,0x29,0x52,0x4e,0x58,0xbb,0x40,0x6e,0x16,0xb0,0x11,0xb,
  0x2,0xe6,0x7e,0x90,0x8c,0xbe,0xd7,0x6a,0xe5,0x44,0x55,0x47,0xa5,0x9,0x31,0x6f,
  0xc9,0xd4,0xe5,0x73,0x68,0xea,0x41,0x2e,0xe5,0xa,0x8c,0x3,0xc6,0xd1,0x50,0xf3,
  0xe8,0x59,0xb,0xff,0x9c,0x32,0x39,0xed,0x59,0x76,0x72,0x2,0xf0,0xe2,0xf9,0xd4,
  0x2f,0x54,0x89,0x86,0x51,0x35,0x59,0xc1,0x44,0x45,0xf9,0x7a,0x23,0x12,0x4d,0xb5,
  0xf2,0xd5,0x41,0x54,0x29,0x42,0x23,0xa5,0x10,0xd5,0xc7,0x85,0xfa,0xd0,0xd0,0x9e,
  0x74,0x2f,0xaf,0x7,0xd,0xd5,0x95,0x69,0x1f,0x59,0x2a,0xab,0xf5,0xb2,0x5f,0xdc,
  0x0,0xc4,0x25,0xbb,0x2d,0xeb,0x4f,0xd,0xdd,0x5d,0xe8,0x69,0x59,0xf0,0xda,0xaf,
  0xec,0x5b,0x8d,0x8,0xca,0x5,0x85,0xa7,0x8c,0x38,0xaf,0xf4,0x82,0x66,0x39,0x37,
  0x13,0x1e,0xb1,0x6a,0xd1,0x11,0xc4,0x36,0xcd,0x2b,0xe1,0xb9,0x86,0xce,0x4c,0x64,
  0x67,0xfc,0x96,0x79,0xef,0xf9,0x27,0x88,0x49,0xda,0xfb,0xa5,0xe2,0xf7,0x85,0x29,
  0xf7,0xc5,0x5d,0x87,0xae,0xee,0x27,0xce,0x6e,0x2a,0xba,0x2e,0xe,0xa5,0x16,0x8e,
  0x71,0x53,0x26,0x62,0x3b,0xc4,0x33,0xf2,0xe0,0xb6,0xcf,0x63,0x73,0x2f,0xb3,0x76,
  0xc4,0xd0,0xe3,0x33,0x43,0x71,0xc9,0xa0,0xd8,0x2b,0x15,0xb9,0x6,0xd7,0xc0,0x87,
  0xd4,0x93,0x6f,0x3e,0xe5,0x81,0xf4,0x9f,0xbd,0xa4,0xd3,0x9a,0x27,0x22,0x8a,0xc4,
  0xf4,0x52,0x5c,0x42,0x2c,0x51,0x5a,0xcc,0x14,0x3d,0x8d,0xcc,0x14,0x3b,0x1d,0x83,
  0x95,0xa6,0xba,0x3b,0x16,0xaf,0x14,0xee,0xb,0xf0,0x95,0x3e,0xd8,0x70,0x53,0xf8,
  0xf8,0xd9,0x63,0x5,0x2e,0xaa,0x8,0xca,0x4f,0x9b,0x5,0x11,0xa9,0x68,0xcc,0x91,
  0xf1,0x2e,0xe1,0x3e,0x8f,0x38,0x44,0xc5,0x9f,0x72,0xde,0xcd,0xae,0x44,0x15,0xbb,
  0xbd,0x3,0x3,0x10,0xc1,0x94,0x46,0x97,0xf0,0xbd,0xe1,0x83,0x1d,0x82,0xe3,0x64,
  0x8d,0xa7,0xbb,0xc4,0x99,0x4c,0x7a,0xd3,0x69,0x2f,0xc,0x9d,0xa7,0xc6,0x12,0xef,
  0xab,0x45,0x8a,0x7,0x84,0x4c,0x29,0x4d,0x82,0x9e,0x67,0x63,0xa8,0x4b,0xcf,0x8a,
  0x1e,0x11,0x8a,0xf3,0xd3,0x31,0x64,0xfc,0xa8,0x32,0x93,0x26,0xca,0x73,0x1a,0x4d,
  0x80,0xc7,0x6f,0x1b,0x7b,0x8b,0x10,0x12,0x29,0xd,0x3c,0xe,0x90,0x95,0x1f,0xc9,
  0x21,0x2,0xe1,0x4f,0x7b,0xbf,0xba,0xb1,0x89,0x87,0x1a,0x3c,0xc7,0xbf,0x6a,0x6,
  0xb2,0xf5,0x6a,0x69,0x58,0x70,0x31,0xc2,0xb6,0xd7,0x10,0xaa,0xe7,0xca,0x47,0xa2,
  0x86,0xee,0x2d,0xc9,0xdb,0x4d,0xf9,0xd5,0x98,0xab,0x8e,0xa7,0xd1,0xd0,0xad,0xec,
  0x18,0x2c,0xab,0x32,0xd9,0xc2,0x27,0x21,0x52,0x23,0x1e,0x73,0x98,0xc7,0xae,0xf2,
  0x63,0x1e,0x44,0x59,0x49,0x5f,0xe0,0x17,0xeb,0x9c,0x81,0xe2,0x5b,0x18,0x74,0xb1,
  0x91,0xd5,0xca,0xc,0xc,0x9c,0xbc,0x35,0x3b,0x89,0xaa,0xca,0x77,0x15,0x93,0x65,
  0x92,0x55,0x7e,0xad,0xd5,0x58,0x4d,0xfc,0xed,0x6a,0xad,0xe4,0x5a,0x6a,0x76,0x18,
  0x5,0x6d,0xac,0x9f,0x49,0xb6,0xc8,0x3e,0x9f,0xc,0x2d,0x94,0xe6,0x6d,0xad,0xd8,
  0x79,0xd2,0x8d,0x51,0x3d,0x48,0x11,0x37,0x1,0x9d,0xa1,0x7,0xe9,0xc5,0x76,0xd8,
  0xfc,0x0,0x63,0xfd,0x3,0x3c,0x32,0xe6,0xcb,0x85,0x1a,0xe6,0xde,0xd6,0x9e,0xf2,
  0xd8,0xe3,0x63,0x7f,0xa,0xda,0x55,0xef,0x8c,0xbf,0xff,0xd4,0x8f,0xe3,0xad,0x2f,
  0x34,0x90,0xb4,0x4d,0xd7,0x1d,0xb,0xca,0x2a,0x3,0x2b,0x46,0x59,0x77,0x28,0x18,
  0x2,0xf6,0x34,0xa4,0x91,0x60,0xab,0xec,0x2d,0x64,0x3f,0x86,0xd9,0x42,0x72,0x9a,
  0x2e,0x8f,0xd6,0x97,0xb2,0x8c,0xe8,0x94,0x7b,0x77,0xf0,0xa6,0xbe,0xf0,0x43,0xe1,
  0xd1,0x10,0x5c,0x96,0xf0,0x5,0x86,0x54,0x86,0x90,0xb7,0xca,0x15,0x17,0x3f,0x2d,
  0x7c,0x2c,0x17,0xae,0x70,0x61,0x90,0x9c,0xcc,0x61,0xd0,0xf9,0x21,0x39,0xe,0x18,
  0xfd,0xd2,0xb3,0x9b,0xf6,0xbe,0x71,0x7a,0x3,0x7e,0xf8,0x60,0x70,0xa8,0x17,0xe7,
  0x8a,0xb0,0xf0,0xa7,0x65,0x12,0x75,0xbd,0xb4,0x71,0xe,0xaa,0x61,0x31,0x17,0xd5,
  0xb0,0x9e,0x93,0x66,0x33,0x2c,0x96,0xf,0xaa,0x69,0x24,0x89,0xc7,0xf,0x36,0x42,
  0x5,0x6a,0xda,0xa6,0xad,0x24,0x5e,0x8b,0xb0,0x1c,0x10,0x96,0x33,0xb2,0x44,0x3e,
  0x3d,0x80,0xcc,0x21,0x4c,0x2d,0x1a,0x37,0xd0,0x22,0xc,0x83,0x1,0xf,0xc2,0x88,
  0x4,0xe2,0x86,0x5c,0xc9,0x81,0x50,0x1d,0x94,0x95,0x2c,0x6,0x15,0xa1,0xe6,0x98,
  0xd0,0xb0,0xd5,0x23,0x42,0x8e,0x5d,0x8b,0xba,0x20,0x94,0x46,0xe5,0xe2,0x20,0x4e,
  0x13,0xa8,0x59,0x9f,0xa9,0x76,0x64,0x97,0x88,0xe,0xcd,0x5c,0x57,0x94,0x5b,0x4f,
  0x55,0xbb,0x66,0xa7,0x88,0x80,0x59,0x99,0xc7,0x87,0x1f,0xed,0xe6,0x64,0x1a,0xb8,
  0xa,0xb,0xdd,0xce,0xde,0xc2,0x9c,0x20,0xbf,0x94,0x1b,0xc2,0xc4,0x64,0x18,0xc9,
  0x6e,0x18,0x80,0x54,0xc3,0x3c,0x3f,0xd2,0xc0,0x25,0xee,0xa4,0xe0,0x3f,0x1c,0x1d,
  0x11,0xc7,0x29,0x5a,0xcf,0xad,0x42,0xa6,0x32,0x72,0x43,0x6,0x95,0x31,0x8,0xc4,
  0x34,0xae,0x8e,0x7e,0x81,0x7d,0xb5,0xca,0x9d,0xc2,0xb2,0x94,0x95,0xd8,0x62,0x21,
  0xd4,0x7e,0xe6,0x96,0x86,0x9e,0x2c,0x48,0xa7,0xe,0x7a,0xf,0x43,0x98,0xe2,0xfe,
  0xd,0x6,0x6c,0xfb,0xf0,0xc5,0xfe,0x69,0xc7,0x21,0x35,0x67,0x12,0x1a,0x9a,0xe7,
  0xcd,0x14,0x81,0xb0,0x6c,0xad,0x9a,0x1d,0xbe,0x86,0x29,0x79,0xcf,0x96,0x1a,0x35,
  0xe4,0x80,0xd2,0xd1,0x8b,0x4d,0xc8,0xb4,0x78,0xab,0xf5,0x1a,0x57,0x1a,0x39,0xb6,
  0x6c,0x5b,0x67,0x5,0x57,0xc2,0x3f,0x41,0xf6,0x92,0x10,0xf3,0x95,0x45,0x88,0x99,
  0xc6,0xda,0xf1,0xaa,0x86,0xd9,0x74,0x2d,0x14,0xb9,0xa,0xd,0xca,0xed,0x88,0xef,
  0x9e,0x6,0x65,0x37,0xa0,0xc4,0x57,0xa5,0x41,0xb9,0xd1,0x83,0xca,0xb8,0x14,0xfd,
  0xf0,0xfa,0xf1,0x72,0xe0,0xd9,0xfe,0xf3,0x76,0xab,0x85,0x1c,0x98,0xda,0xb0,0xde,
  0x52,0xe0,0x96,0x2,0x57,0x97,0x30,0xc7,0xb8,0xef,0x19,0xe8,0xd8,0xfd,0x9e,0x82,
  0xdc,0x3e,0xee,0xef,0x13,0xb5,0x51,0xff,0x58,0xf9,0x3d,0xc3,0xa1,0xf1,0x99,0x86,
  0xe2,0xb3,0x9,0x55,0x30,0x1d,0x8b,0x28,0xc3,0x57,0xa7,0xd2,0xfe,0x7e,0xe7,0xa0,
  0x73,0x80,0x54,0xea,0x3c,0x19,0xec,0xed,0x75,0xbb,0x16,0xb,0x36,0x1a,0x5b,0x22,
  0xfd,0x4e,0x89,0xf4,0xb3,0xb0,0xcd,0x25,0xb,0x1f,0x77,0x24,0x99,0x3b,0xc,0xe5,
  0xb3,0x80,0x46,0xc,0xdb,0x8d,0xcd,0xfe,0x56,0x18,0xa3,0x7b,0x70,0x70,0xd6,0xed,
  0x4b,0xc6,0xd8,0xeb,0x1f,0xf,0x9e,0x1b,0x76,0xea,0xd2,0xd8,0x32,0xc6,0x77,0xca,
  0x18,0xd5,0xa9,0xa6,0xd0,0xeb,0x72,0xc2,0x83,0x74,0xe4,0x55,0x29,0x6f,0xcd,0x3d,
  0x8a,0x77,0x2e,0xe6,0x3e,0xc1,0x31,0x68,0x11,0xe1,0x94,0x70,0x4e,0xbb,0x63,0x26,
  0x9d,0x35,0x8,0xa7,0x36,0xd9,0x64,0x88,0x26,0x98,0xfb,0x71,0xfb,0x2c,0x8,0xc6,
  0x62,0xc4,0xad,0x43,0x2c,0xe5,0xa4,0xf2,0xe2,0xa4,0x3d,0x38,0xee,0x48,0x52,0x39,
  0xec,0x77,0x5e,0x9c,0x58,0x92,0x8a,0x3d,0xa1,0x58,0x34,0x6d,0x55,0x22,0x59,0x8d,
  0x44,0x56,0x20,0x90,0x15,0xc9,0x63,0x83,0xc4,0xb1,0x11,0xd2,0x30,0xd1,0x41,0xbd,
  0x94,0xfc,0xd3,0xe5,0x27,0xf7,0xb9,0x73,0xa1,0x17,0xf1,0x21,0x16,0x7d,0x30,0x74,
  0x71,0x9c,0x5e,0x85,0xfd,0x4b,0xe2,0x1b,0x3c,0x1e,0x6a,0x9a,0xde,0x6d,0x8f,0x86,
  0x6e,0x8f,0x86,0xae,0x7e,0x34,0x34,0x67,0xc6,0x8f,0xea,0x80,0x28,0x6,0x1,0xf4,
  0xa,0xc,0xe0,0x15,0xa3,0x6e,0x9,0xc5,0x7c,0x55,0x2b,0x31,0x9f,0x74,0xfb,0x6c,
  0xfb,0xe7,0xb6,0x2b,0x4d,0xeb,0x6f,0x9e,0x57,0x9f,0x13,0x59,0x1c,0xc5,0xdf,0xc8,
  0xde,0x79,0x59,0xb0,0x55,0xa1,0xb,0xd,0x3d,0xa3,0xd4,0x57,0x72,0xbe,0xc0,0x96,
  0xbb,0x39,0x8c,0xdb,0x90,0x17,0xde,0xc0,0x21,0xa7,0xaa,0xd4,0xd4,0x20,0x4a,0x2e,
  0xa6,0xbd,0x2c,0xf3,0x74,0x2f,0x17,0xd6,0x7f,0xda,0xc2,0x3f,0xc7,0x50,0xfa,0x9a,
  0xaa,0x2f,0xb8,0x4f,0x91,0x85,0x52,0xfd,0x9,0xd,0x86,0xc2,0xb5,0x8,0xab,0xb6,
  0x8a,0x57,0x78,0xc8,0x8a,0x3f,0xb0,0xd6,0xfb,0x5,0xb,0xe7,0x5e,0xb4,0x55,0x7b,
  0xa,0xdf,0x8e,0xda,0xeb,0xee,0x7f,0x24,0x91,0xcf,0x8,0xef,0x8e,0xbe,0xf6,0x47,
  0x62,0xab,0xf8,0x14,0x36,0x74,0xca,0xd1,0xf2,0x6e,0x8e,0xdc,0x1,0x79,0x8c,0x57,
  0x73,0x3a,0x1b,0xbd,0x54,0x93,0xda,0x29,0xaa,0xd0,0x4f,0x69,0xd2,0xca,0x17,0x42,
  0x16,0xfa,0xa9,0x71,0x1f,0xa4,0x5b,0x1e,0xbe,0x23,0x14,0x1b,0x70,0xdf,0x65,0xb7,
  0xe4,0x8f,0xa4,0x43,0x8e,0x8e,0x8e,0x48,0x2b,0x5e,0xd0,0x51,0x37,0xbc,0x89,0xcd,
  0xd9,0x53,0xc4,0x3a,0x77,0x40,0xf6,0xc,0x77,0x40,0x6a,0xec,0xc4,0xd6,0x8e,0x91,
  0xd3,0x99,0x6a,0x9c,0xa7,0xb6,0x8d,0x95,0x11,0x35,0x56,0xa2,0xd6,0x88,0x99,0x11,
  0x8b,0x53,0xde,0xf9,0x9b,0xec,0x65,0x58,0xe1,0xf4,0x76,0x92,0x6d,0xf5,0x13,0xdc,
  0x1a,0xf,0x6d,0x69,0xb,0x91,0xd8,0xf0,0xf3,0x18,0xe6,0x86,0x98,0x17,0xd0,0x57,
  0xf5,0xd2,0x6a,0x14,0x59,0xbc,0x61,0x3,0x46,0x66,0x11,0x9d,0x23,0xa4,0x91,0x5d,
  0xc9,0x0,0xfd,0x73,0x2e,0x7b,0xd6,0x75,0xf5,0x88,0x7,0x6d,0x4f,0xb6,0x57,0x6e,
  0x1e,0x82,0x3d,0xd5,0xdd,0x1a,0x58,0x3d,0xf2,0x4f,0xe5,0xb6,0xe,0x15,0xd2,0x50,
  0x8d,0x73,0x17,0x3f,0x4c,0x81,0x53,0x88,0xd8,0x95,0x39,0x6f,0x69,0x18,0x3a,0xe4,
  0xd7,0x5f,0x8b,0x53,0xff,0xf3,0xcf,0x7f,0xfc,0xf7,0x5f,0xbf,0x3b,0xf2,0x48,0xda,
  0xc1,0xe0,0xf9,0x99,0x3e,0x47,0x31,0x38,0x3b,0x39,0x3b,0xab,0xb7,0x81,0xd1,0xb5,
  0xdb,0xeb,0xac,0xb9,0xbd,0xb9,0xd2,0x82,0x62,0x1a,0x72,0xb0,0x66,0x5a,0x6f,0x9d,
  0x7b,0x8d,0xcd,0xce,0xcd,0x68,0xa5,0x73,0xf6,0xe2,0xb4,0xab,0xb6,0x95,0xd4,0x49,
  0x97,0x7a,0x95,0xaf,0x49,0x1f,0xeb,0x6e,0x85,0x22,0x1e,0x22,0xd7,0xd7,0x9d,0x99,
  0x21,0xb4,0xe9,0xe0,0xe4,0xc,0xe7,0x66,0x9f,0x93,0xea,0xd,0xb4,0xfd,0xc6,0xe6,
  0x62,0x29,0x62,0x63,0x9c,0xcd,0x3c,0x9e,0x5c,0x79,0x3c,0xc3,0xcf,0x17,0x56,0x37,
  0xfa,0x10,0x1b,0xe,0x1f,0x36,0x77,0xd7,0xed,0x9c,0xfa,0x73,0xea,0x91,0x63,0xd7,
  0x55,0x5b,0xc,0x5f,0xe5,0xb6,0x5b,0x85,0x47,0xb0,0xbe,0xec,0xb6,0xee,0x52,0x7c,
  0x99,0x9c,0xc5,0x2e,0xd0,0xe3,0xbf,0xec,0x86,0xf6,0xf1,0x23,0xbb,0x51,0x36,0xf2,
  0x20,0x56,0x64,0x36,0x79,0xdb,0xed,0x81,0x9d,0xeb,0x45,0xdd,0xc,0x38,0xf3,0x5c,
  0x4b,0x42,0xc7,0x25,0x8,0x15,0x7b,0xbf,0xf6,0x67,0x73,0x3b,0x4a,0x5a,0x33,0xe2,
  0x9f,0x79,0x30,0x9f,0x9b,0x80,0x86,0x59,0x70,0x59,0x73,0x79,0x1e,0x51,0xe0,0x13,
  0xac,0xf2,0x6d,0xe8,0x8,0xdc,0xe2,0xd7,0xea,0x6c,0x33,0x66,0xe9,0x45,0xad,0x95,
  0xd6,0xcd,0x6f,0xa4,0x9b,0x2c,0xea,0x9e,0xae,0x33,0x4b,0x18,0x45,0xfa,0x62,0x7a,
  0x25,0x4e,0xc4,0x6d,0xd,0xeb,0xb,0xe2,0x58,0x2d,0xce,0xf8,0x25,0x8c,0x4f,0x2d,
  0xd1,0xfd,0x2c,0xa3,0xc6,0x5d,0xe2,0xc,0x28,0xf7,0x9c,0x5f,0xb6,0x96,0x57,0x91,
  0xff,0x5b,0xb0,0xbc,0x55,0x88,0x4f,0x7,0xa3,0x35,0x79,0x6f,0x95,0xe8,0x37,0xcf,
  0x79,0x6a,0x9b,0x82,0x43,0x28,0x4c,0x1a,0x3f,0xa,0x9f,0xc5,0x3f,0x76,0x88,0x56,
  0xf9,0x74,0xcb,0x83,0x55,0xf9,0xbf,0x5,0x6b,0x5c,0xe5,0xc0,0x3b,0x4,0x4a,0x6b,
  0x1d,0x75,0xaf,0xf8,0xd9,0x87,0xaa,0xdc,0x5f,0xe2,0xa8,0x3b,0xde,0x6d,0x4c,0x47,
  0x19,0xf1,0xa9,0xc8,0x15,0x2f,0x39,0xe2,0x5,0x4c,0x39,0x70,0xc9,0x51,0x7a,0x4,
  0xcb,0x32,0xe3,0x29,0x7f,0x3c,0xd9,0xc7,0x21,0x85,0x33,0xfd,0xac,0x48,0xad,0x97,
  0x65,0x8e,0xce,0x52,0xd7,0xcd,0xfc,0x60,0x69,0xa3,0x56,0x71,0x88,0x5c,0x3f,0xd8,
  0xfd,0x36,0x59,0x1a,0x29,0x97,0xd9,0x1c,0xce,0x3,0x9c,0x8,0x5c,0xae,0x54,0x90,
  0xec,0x9b,0x5a,0xd9,0xec,0xef,0x40,0x21,0xf2,0x4a,0x47,0xf5,0xd4,0x2a,0x22,0xaf,
  0xe2,0x3a,0x25,0x7c,0x23,0x37,0x58,0xb7,0x97,0x28,0xd4,0x5b,0xb7,0x97,0x28,0x24,
  0xd6,0xbf,0x44,0x51,0x2f,0xc5,0x7c,0x6a,0x7a,0xf9,0xd3,0xfd,0xce,0xfd,0xce,0xff,
  0x1,0x6,0xcf,0xa7,0x63,
  
};

static const unsigned char qt_resource_name[] = {
  // main.qml
  0x0,0x8,
  0x8,0x1,0x5a,0x5c,
  0x0,0x6d,
  0x0,0x61,0x0,0x69,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/main.qml
  0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0xa2,0x52,0xeb,0x80,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)(); }
   } dummy;
}
