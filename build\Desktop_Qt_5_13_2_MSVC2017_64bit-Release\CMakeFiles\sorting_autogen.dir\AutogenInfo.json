{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen", "CMAKE_BINARY_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "CMAKE_CURRENT_BINARY_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "CMAKE_CURRENT_SOURCE_DIR": "E:/software/sorting", "CMAKE_EXECUTABLE": "C:/Program Files/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["E:/software/sorting/CMakeLists.txt", "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/.qtc/package-manager/auto-setup.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystem.cmake.in", "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeJOMFindMake.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Initialize.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Determine-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCompilerIdDetection.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ADSP-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMCC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/ARMClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/AppleClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Borland-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Cray-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CrayClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Embarcadero-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Fujitsu-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GHS-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/HP-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IAR-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Intel-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/MSVC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVHPC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/NVIDIA-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/OrangeC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PGI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/PathScale-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SCO-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/TIClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Tasking-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Watcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XL-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeFindBinUtils.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-FindBinUtils.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in", "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeGenericSystem.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/WindowsPaths.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/Clang.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/GNU.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Clang-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-Clang.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-GNU.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCCompiler.cmake.in", "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeRCInformation.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-windres.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitIncludeInfo.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseImplicitLinkInfo.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseLibraryArchitecture.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeDetermineCompileFeatures.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Internal/FeatureTesting.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in", "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/3.29.3/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/Platform/Windows-GNU-CXX-ABI.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeParseArguments.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "E:/software/sorting/qml.qrc"], "CMAKE_SOURCE_DIR": "E:/software/sorting", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["E:/software/sorting/sorting.h", "MU", "EWIEGA46WW/moc_sorting.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen/include", "MOC_COMPILATION_FILE": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_QML_LIB", "QT_QUICK_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release", "E:/software/sorting", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++", "D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32", "D:/Qt/Qt5.13.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward", "D:/LLVM/lib/clang/12.0.0/include", "D:/Qt/Qt5.13.2/Tools/mingw730_32/i686-w64-mingw32/include", "D:/Qt/Qt5.13.2/Tools/mingw730_32/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["D:/LLVM/bin/clang++.exe", "-std=gnu++11", "-dM", "-E", "-c", "C:/Program Files/CMake/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp", "--target=i686-w64-mingw32"], "MOC_PREDEFS_FILE": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/sorting_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 13, "SETTINGS_FILE": "E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_64bit-Release/CMakeFiles/sorting_autogen.dir/AutogenUsed.txt", "SOURCES": [["E:/software/sorting/main.cpp", "MU", null], ["E:/software/sorting/sorting.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}