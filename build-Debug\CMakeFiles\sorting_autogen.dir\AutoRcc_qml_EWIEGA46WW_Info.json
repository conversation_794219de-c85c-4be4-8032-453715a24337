{"BUILD_DIR": "E:/software/sorting/build-Debug/sorting_autogen", "CMAKE_BINARY_DIR": "E:/software/sorting/build-Debug", "CMAKE_CURRENT_BINARY_DIR": "E:/software/sorting/build-Debug", "CMAKE_CURRENT_SOURCE_DIR": "E:/software/sorting", "CMAKE_SOURCE_DIR": "E:/software/sorting", "CROSS_CONFIG": false, "GENERATOR": "Visual Studio 17 2022", "INCLUDE_DIR": "E:/software/sorting/build-Debug/sorting_autogen/include", "INCLUDE_DIR_Debug": "E:/software/sorting/build-Debug/sorting_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "E:/software/sorting/build-Debug/sorting_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "E:/software/sorting/build-Debug/sorting_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "E:/software/sorting/build-Debug/sorting_autogen/include_Release", "INPUTS": ["E:/software/sorting/main.qml"], "LOCK_FILE": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": true, "OPTIONS": ["-name", "qml"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_qml.cpp", "RCC_EXECUTABLE": "D:/Qt/Qt5.13.2/5.13.2/msvc2017_64/bin/rcc.exe", "RCC_LIST_OPTIONS": [], "SETTINGS_FILE": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used.txt", "SETTINGS_FILE_Debug": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used_Debug.txt", "SETTINGS_FILE_MinSizeRel": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "E:/software/sorting/build-Debug/CMakeFiles/sorting_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used_Release.txt", "SOURCE": "E:/software/sorting/qml.qrc", "USE_BETTER_GRAPH": false, "VERBOSITY": 0}