# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.29

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: CMAKE_TRY_COMPILE
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = E$:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Debug\CMakeFiles\CMakeScratch\TryCompile-m2l235\
# =============================================================================
# Object build statements for EXECUTABLE target cmTC_5fd29


#############################################
# Order-only phony target for cmTC_5fd29

build cmake_object_order_depends_target_cmTC_5fd29: phony || .

build CMakeFiles\cmTC_5fd29.dir\CMakeCXXCompilerABI.cpp.obj: CXX_COMPILER__cmTC_5fd29_unscanned_Debug C$:\Program$ Files\CMake\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp || cmake_object_order_depends_target_cmTC_5fd29
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /W3 /GR /EHsc  /MDd /Zi /Ob0 /Od /RTC1
  OBJECT_DIR = CMakeFiles\cmTC_5fd29.dir
  OBJECT_FILE_DIR = CMakeFiles\cmTC_5fd29.dir
  TARGET_COMPILE_PDB = CMakeFiles\cmTC_5fd29.dir\
  TARGET_PDB = cmTC_5fd29.pdb


# =============================================================================
# Link build statements for EXECUTABLE target cmTC_5fd29


#############################################
# Link the executable cmTC_5fd29.exe

build cmTC_5fd29.exe: CXX_EXECUTABLE_LINKER__cmTC_5fd29_Debug CMakeFiles\cmTC_5fd29.dir\CMakeCXXCompilerABI.cpp.obj
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /W3 /GR /EHsc  /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:X86  /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\cmTC_5fd29.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\cmTC_5fd29.dir\
  TARGET_FILE = cmTC_5fd29.exe
  TARGET_IMPLIB = cmTC_5fd29.lib
  TARGET_PDB = cmTC_5fd29.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Debug\CMakeFiles\CMakeScratch\TryCompile-m2l235 && "C:\Program Files\CMake\bin\cmake-gui.exe" -SE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Debug\CMakeFiles\CMakeScratch\TryCompile-m2l235 -BE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Debug\CMakeFiles\CMakeScratch\TryCompile-m2l235"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D E:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Debug\CMakeFiles\CMakeScratch\TryCompile-m2l235 && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Debug\CMakeFiles\CMakeScratch\TryCompile-m2l235 -BE:\software\sorting\build\Desktop_Qt_5_13_2_MSVC2017_32bit-Debug\CMakeFiles\CMakeScratch\TryCompile-m2l235"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build cmTC_5fd29: phony cmTC_5fd29.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: E:/software/sorting/build/Desktop_Qt_5_13_2_MSVC2017_32bit-Debug/CMakeFiles/CMakeScratch/TryCompile-m2l235

build all: phony cmTC_5fd29.exe

# =============================================================================
# Built-in targets


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
